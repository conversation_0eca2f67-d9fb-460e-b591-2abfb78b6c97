<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO 硬盘性能对比图表集</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
            margin-bottom: 20px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .chart-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .chart-container {
            text-align: center;
            margin: 20px 0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .chart-description {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 15px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        .legend {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .legend h3 {
            margin-top: 0;
            color: #856404;
        }
        .legend ul {
            margin: 0;
            padding-left: 20px;
        }
        .legend li {
            margin: 5px 0;
        }
        .performance-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .summary-card {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .status-pass {
            color: #27ae60;
            font-weight: bold;
        }
        .status-fail {
            color: #e74c3c;
            font-weight: bold;
        }
        .navigation {
            position: sticky;
            top: 20px;
            background-color: #34495e;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .navigation a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            padding: 8px 12px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .navigation a:hover {
            background-color: #2c3e50;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 FIO 硬盘性能对比图表集</h1>
        
        <div class="navigation">
            <a href="#overview">总览</a>
            <a href="#trend-charts">时间序列对比</a>
            <a href="#summary-charts">性能汇总对比</a>
            <a href="#individual-charts">单项测试对比</a>
        </div>
        
        <div class="summary">
            <h2>🎯 测试概览</h2>
            <p><strong>基准测试 (测试1):</strong> HP 硬盘 - /check/fio-web-controller/hp/</p>
            <p><strong>对比测试 (测试2):</strong> 新硬盘 - /check/fio_results_20250716_121731/</p>
            <p><strong>测试时间:</strong> 2025年7月16日</p>
            <p><strong>测试标准:</strong> 性能比率 ≥ 1.0 为 PASS，< 1.0 为 FAIL</p>
        </div>

        <div class="performance-summary">
            <div class="summary-card">
                <h4>4K 混合读写</h4>
                <p>读IOPS: 0.988 <span class="status-fail">FAIL</span></p>
                <p>写IOPS: 0.986 <span class="status-fail">FAIL</span></p>
                <p>整体: <span class="status-fail">FAIL</span></p>
            </div>
            <div class="summary-card">
                <h4>128K 混合读写</h4>
                <p>读IOPS: 1.047 <span class="status-pass">PASS</span></p>
                <p>写IOPS: 1.048 <span class="status-pass">PASS</span></p>
                <p>整体: <span class="status-pass">PASS</span></p>
            </div>
            <div class="summary-card">
                <h4>1M 混合读写</h4>
                <p>读IOPS: 1.084 <span class="status-pass">PASS</span></p>
                <p>写IOPS: 1.081 <span class="status-pass">PASS</span></p>
                <p>整体: <span class="status-pass">PASS</span></p>
            </div>
        </div>

        <div class="legend">
            <h3>📖 图表说明</h3>
            <ul>
                <li><strong>实线:</strong> 测试1 (基准硬盘 - HP)</li>
                <li><strong>虚线:</strong> 测试2 (对比硬盘)</li>
                <li><strong>蓝色/绿色:</strong> 读 IOPS</li>
                <li><strong>红色/橙色:</strong> 写 IOPS</li>
                <li><strong>时间轴:</strong> 测试持续时间 (分钟)</li>
            </ul>
        </div>

        <section id="overview">
            <h2>🔍 综合对比总览</h2>
            <div class="chart-section">
                <div class="chart-container">
                    <div class="chart-title">所有测试项目综合对比</div>
                    <div class="chart-description">
                        展示4K、128K、1M三种块大小的完整时间序列性能对比，可以清晰看到两个硬盘在不同负载下的表现差异。
                    </div>
                    <img src="all_tests_comparison_overview.png" alt="综合对比总览">
                </div>
            </div>
        </section>

        <section id="trend-charts">
            <h2>📈 时间序列对比图表</h2>
            
            <div class="chart-section">
                <div class="chart-container">
                    <div class="chart-title">4K 混合读写性能时间序列对比</div>
                    <div class="chart-description">
                        小文件随机读写性能对比。测试2在4K测试中表现略低于基准，读写IOPS都有1-2%的下降。
                        这可能影响数据库和系统响应性能。
                    </div>
                    <img src="mixed_rw_4k_comparison_chart.png" alt="4K性能对比">
                </div>
            </div>

            <div class="chart-section">
                <div class="chart-container">
                    <div class="chart-title">128K 混合读写性能时间序列对比</div>
                    <div class="chart-description">
                        中等块大小读写性能对比。测试2表现优秀，读写IOPS都有约4.8%的提升。
                        适合中等文件处理和一般应用场景。
                    </div>
                    <img src="mixed_rw_128k_comparison_chart.png" alt="128K性能对比">
                </div>
            </div>

            <div class="chart-section">
                <div class="chart-container">
                    <div class="chart-title">1M 混合读写性能时间序列对比</div>
                    <div class="chart-description">
                        大文件顺序读写性能对比。测试2表现最佳，读写IOPS都有8%以上的显著提升。
                        非常适合大文件传输、视频编辑和数据备份场景。
                    </div>
                    <img src="mixed_rw_1M_comparison_chart.png" alt="1M性能对比">
                </div>
            </div>
        </section>

        <section id="summary-charts">
            <h2>📊 性能汇总对比</h2>
            
            <div class="chart-section">
                <div class="chart-container">
                    <div class="chart-title">性能比率汇总对比</div>
                    <div class="chart-description">
                        以基准线(1.0)为参考的性能比率对比图表，清晰显示各项测试的Pass/Fail状态。
                    </div>
                    <img src="performance_comparison_chart.png" alt="性能比率对比">
                </div>
            </div>
        </section>

        <section id="individual-charts">
            <h2>📋 详细分析报告</h2>
            <div class="chart-section">
                <p>除了图表对比，我们还提供了详细的分析报告：</p>
                <ul>
                    <li><a href="performance_report.html" target="_blank">📋 完整HTML报告</a> - 包含详细分析和建议</li>
                    <li><a href="performance_chart.html" target="_blank">📊 交互式图表</a> - 可交互的性能对比图表</li>
                    <li><a href="performance_comparison_detailed.csv" target="_blank">📈 详细CSV数据</a> - 原始数据和计算结果</li>
                    <li><a href="performance_summary.md" target="_blank">📝 Markdown总结</a> - 简洁的文本格式总结</li>
                </ul>
            </div>
        </section>

        <h2>💡 关键发现与建议</h2>
        <div class="chart-section">
            <h3>✅ 优势方面</h3>
            <ul>
                <li><strong>大文件处理能力强:</strong> 1M测试提升8%+，适合视频编辑、数据备份</li>
                <li><strong>中等负载表现优秀:</strong> 128K测试稳定提升4.8%</li>
                <li><strong>整体稳定性好:</strong> 2/3测试项目通过基准</li>
            </ul>
            
            <h3>⚠️ 需要关注</h3>
            <ul>
                <li><strong>小文件性能下降:</strong> 4K测试性能下降1-2%，可能影响系统响应</li>
                <li><strong>随机访问优化空间:</strong> 小块随机读写需要改进</li>
            </ul>
            
            <h3>🎯 使用建议</h3>
            <ul>
                <li><strong>推荐场景:</strong> 大文件传输、视频编辑、数据仓库分析</li>
                <li><strong>需要优化:</strong> 数据库应用、系统启动、小文件密集型应用</li>
                <li><strong>优化建议:</strong> 检查缓存设置、监控硬盘健康、考虑系统调优</li>
            </ul>
        </div>

        <div class="footer">
            <p>📅 报告生成时间: 2025年7月16日</p>
            <p>🔧 测试工具: FIO (Flexible I/O Tester) + Gnuplot + Python</p>
            <p>📊 分析方法: 时间序列对比 + 基准性能评估</p>
        </div>
    </div>
</body>
</html>
