#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIO Performance Comparison Tool
比较两个硬盘测试结果，以测试1（HP）作为基准
"""

import os
import re
from pathlib import Path

class FIOPerformanceComparator:
    def __init__(self, baseline_path, test_path):
        self.baseline_path = Path(baseline_path)
        self.test_path = Path(test_path)
        self.results = {}
        
    def parse_summary_file(self, file_path):
        """解析FIO汇总文件，提取关键性能指标"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # 提取读写性能数据
            read_match = re.search(r'read: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
            write_match = re.search(r'write: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
            
            def parse_iops(iops_str):
                if 'k' in iops_str:
                    return float(iops_str.replace('k', '')) * 1000
                return float(iops_str)
            
            result = {}
            if read_match:
                result['read_iops'] = parse_iops(read_match.group(1))
                result['read_bw'] = float(read_match.group(2))
            
            if write_match:
                result['write_iops'] = parse_iops(write_match.group(1))
                result['write_bw'] = float(write_match.group(2))
                
            return result
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return {}
    
    def collect_performance_data(self):
        """收集两个测试目录的性能数据"""
        test_types = ['mixed_rw_4k', 'mixed_rw_128k', 'mixed_rw_1M']
        
        for test_type in test_types:
            baseline_file = self.baseline_path / f"{test_type}_summary.txt"
            test_file = self.test_path / f"{test_type}_summary.txt"
            
            if baseline_file.exists() and test_file.exists():
                baseline_data = self.parse_summary_file(baseline_file)
                test_data = self.parse_summary_file(test_file)
                
                self.results[test_type] = {
                    'baseline': baseline_data,
                    'test': test_data
                }
    
    def calculate_performance_ratio(self):
        """计算性能比率和Pass/Fail状态"""
        comparison_results = {}
        
        for test_type, data in self.results.items():
            baseline = data['baseline']
            test = data['test']
            
            comparison = {}
            
            # 计算读性能比率
            if 'read_iops' in baseline and 'read_iops' in test:
                read_iops_ratio = test['read_iops'] / baseline['read_iops']
                read_bw_ratio = test['read_bw'] / baseline['read_bw']
                
                comparison['read_iops_ratio'] = read_iops_ratio
                comparison['read_bw_ratio'] = read_bw_ratio
                comparison['read_status'] = 'PASS' if read_iops_ratio >= 1.0 else 'FAIL'
            
            # 计算写性能比率
            if 'write_iops' in baseline and 'write_iops' in test:
                write_iops_ratio = test['write_iops'] / baseline['write_iops']
                write_bw_ratio = test['write_bw'] / baseline['write_bw']
                
                comparison['write_iops_ratio'] = write_iops_ratio
                comparison['write_bw_ratio'] = write_bw_ratio
                comparison['write_status'] = 'PASS' if write_iops_ratio >= 1.0 else 'FAIL'
            
            # 整体状态
            read_pass = comparison.get('read_status') == 'PASS'
            write_pass = comparison.get('write_status') == 'PASS'
            comparison['overall_status'] = 'PASS' if read_pass and write_pass else 'FAIL'
            
            comparison_results[test_type] = comparison
            
        return comparison_results
    
    def generate_comparison_report(self):
        """生成详细的对比报告"""
        comparison_results = self.calculate_performance_ratio()
        
        print("=" * 80)
        print("FIO 性能对比报告")
        print("=" * 80)
        print(f"基准测试 (测试1): {self.baseline_path}")
        print(f"对比测试 (测试2): {self.test_path}")
        print("=" * 80)
        
        for test_type, comparison in comparison_results.items():
            baseline_data = self.results[test_type]['baseline']
            test_data = self.results[test_type]['test']
            
            print(f"\n【{test_type.upper()}】")
            print("-" * 50)
            
            # 读性能对比
            if 'read_iops_ratio' in comparison:
                print(f"读性能 (Read Performance):")
                print(f"  IOPS: {baseline_data['read_iops']:,.0f} → {test_data['read_iops']:,.0f} "
                      f"(比率: {comparison['read_iops_ratio']:.3f}) [{comparison['read_status']}]")
                print(f"  带宽: {baseline_data['read_bw']:.1f} MiB/s → {test_data['read_bw']:.1f} MiB/s "
                      f"(比率: {comparison['read_bw_ratio']:.3f})")
            
            # 写性能对比
            if 'write_iops_ratio' in comparison:
                print(f"写性能 (Write Performance):")
                print(f"  IOPS: {baseline_data['write_iops']:,.0f} → {test_data['write_iops']:,.0f} "
                      f"(比率: {comparison['write_iops_ratio']:.3f}) [{comparison['write_status']}]")
                print(f"  带宽: {baseline_data['write_bw']:.1f} MiB/s → {test_data['write_bw']:.1f} MiB/s "
                      f"(比率: {comparison['write_bw_ratio']:.3f})")
            
            print(f"整体状态: {comparison['overall_status']}")
        
        # 生成汇总表
        print("\n" + "=" * 80)
        print("性能对比汇总表")
        print("=" * 80)
        print(f"{'测试项目':<15} {'读IOPS比率':<12} {'读状态':<8} {'写IOPS比率':<12} {'写状态':<8} {'整体状态':<8}")
        print("-" * 80)
        
        for test_type, comparison in comparison_results.items():
            read_ratio = comparison.get('read_iops_ratio', 0)
            write_ratio = comparison.get('write_iops_ratio', 0)
            read_status = comparison.get('read_status', 'N/A')
            write_status = comparison.get('write_status', 'N/A')
            overall_status = comparison.get('overall_status', 'N/A')
            
            print(f"{test_type:<15} {read_ratio:<12.3f} {read_status:<8} {write_ratio:<12.3f} {write_status:<8} {overall_status:<8}")
        
        return comparison_results
    
    def create_ascii_chart(self, comparison_results):
        """创建ASCII字符图表"""
        print("\n" + "=" * 80)
        print("性能比率可视化图表 (ASCII)")
        print("=" * 80)

        for test_type, comparison in comparison_results.items():
            print(f"\n【{test_type.upper()}】")
            print("-" * 50)

            # 读性能比率图
            if 'read_iops_ratio' in comparison:
                ratio = comparison['read_iops_ratio']
                status = comparison['read_status']
                bar_length = int(ratio * 50)  # 最大50个字符
                bar = "█" * min(bar_length, 50)
                if bar_length > 50:
                    bar += f" (+{bar_length-50})"

                print(f"读 IOPS: {ratio:.3f} [{status}]")
                print(f"         |{bar}")
                print(f"         |{'─' * 50}")
                print(f"         0    0.5    1.0    1.5    2.0+")

            # 写性能比率图
            if 'write_iops_ratio' in comparison:
                ratio = comparison['write_iops_ratio']
                status = comparison['write_status']
                bar_length = int(ratio * 50)
                bar = "█" * min(bar_length, 50)
                if bar_length > 50:
                    bar += f" (+{bar_length-50})"

                print(f"写 IOPS: {ratio:.3f} [{status}]")
                print(f"         |{bar}")
                print(f"         |{'─' * 50}")
                print(f"         0    0.5    1.0    1.5    2.0+")

        return True

def main():
    # 设置测试路径
    baseline_path = "/check/fio-web-controller/hp"
    test_path = "/check/fio_results_20250716_121731"
    
    # 创建比较器
    comparator = FIOPerformanceComparator(baseline_path, test_path)
    
    # 收集数据
    comparator.collect_performance_data()
    
    # 生成报告
    comparison_results = comparator.generate_comparison_report()

    # 创建ASCII图表
    comparator.create_ascii_chart(comparison_results)

if __name__ == "__main__":
    main()
