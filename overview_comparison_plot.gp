
set terminal png size 1600,1200 font "Arial,12"
set output "/check/all_tests_comparison_overview.png"

set multiplot layout 3,1 title "FIO 性能对比总览 - 测试1(基准) vs 测试2" font "Arial,20"

# 4K 测试
set title "4K 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_4k_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

# 128K 测试
set title "128K 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_128k_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

# 1M 测试
set title "1M 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_1M_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

unset multiplot
