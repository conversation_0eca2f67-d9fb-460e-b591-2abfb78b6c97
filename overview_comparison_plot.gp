
set terminal png size 1800,1400 font "Arial,16"
set output "/check/all_tests_comparison_overview.png"

set multiplot layout 3,1 title "FIO 硬盘性能对比总览\n基准: HP硬盘(实线) vs 对比: 新硬盘(虚线)" font "Arial,22"

# 设置通用样式
set grid
set key outside right top font "Arial,12"

# 4K 测试
set title "4K 混合读写性能对比 (小文件随机访问)" font "Arial,18"
set xlabel "时间 (分钟)" font "Arial,14"
set ylabel "IOPS" font "Arial,14"
set label "基准硬盘表现更好" at graph 0.5, graph 0.9 font "Arial,12" tc rgb "blue" center
plot "/check/mixed_rw_4k_comparison_trend.dat" using 1:2 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#1f77b4" title "基准硬盘(HP) - 读", \
     "" using 1:3 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#ff7f0e" title "基准硬盘(HP) - 写", \
     "" using 1:4 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#2ca02c" title "新硬盘 - 读", \
     "" using 1:5 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#d62728" title "新硬盘 - 写"
unset label

# 128K 测试
set title "128K 混合读写性能对比 (中等块大小)" font "Arial,18"
set xlabel "时间 (分钟)" font "Arial,14"
set ylabel "IOPS" font "Arial,14"
set label "新硬盘表现更好" at graph 0.5, graph 0.9 font "Arial,12" tc rgb "green" center
plot "/check/mixed_rw_128k_comparison_trend.dat" using 1:2 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#1f77b4" title "基准硬盘(HP) - 读", \
     "" using 1:3 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#ff7f0e" title "基准硬盘(HP) - 写", \
     "" using 1:4 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#2ca02c" title "新硬盘 - 读", \
     "" using 1:5 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#d62728" title "新硬盘 - 写"
unset label

# 1M 测试
set title "1M 混合读写性能对比 (大文件顺序访问)" font "Arial,18"
set xlabel "时间 (分钟)" font "Arial,14"
set ylabel "IOPS" font "Arial,14"
set label "新硬盘明显更好" at graph 0.5, graph 0.9 font "Arial,12" tc rgb "green" center
plot "/check/mixed_rw_1M_comparison_trend.dat" using 1:2 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#1f77b4" title "基准硬盘(HP) - 读", \
     "" using 1:3 with linespoints lw 3 pt 7 ps 1.0 lc rgb "#ff7f0e" title "基准硬盘(HP) - 写", \
     "" using 1:4 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#2ca02c" title "新硬盘 - 读", \
     "" using 1:5 with linespoints lw 3 pt 9 ps 1.0 dt 2 lc rgb "#d62728" title "新硬盘 - 写"
unset label

unset multiplot
