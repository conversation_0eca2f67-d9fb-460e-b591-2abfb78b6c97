#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIO Performance Comparison Tool with Charts
比较两个硬盘测试结果，以测试1（HP）作为基准，并生成图表
"""

import os
import re
import subprocess
from pathlib import Path

class FIOPerformanceComparator:
    def __init__(self, baseline_path, test_path):
        self.baseline_path = Path(baseline_path)
        self.test_path = Path(test_path)
        self.results = {}
        
    def parse_summary_file(self, file_path):
        """解析FIO汇总文件，提取关键性能指标"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # 提取读写性能数据
            read_match = re.search(r'read: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
            write_match = re.search(r'write: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
            
            def parse_iops(iops_str):
                if 'k' in iops_str:
                    return float(iops_str.replace('k', '')) * 1000
                return float(iops_str)
            
            result = {}
            if read_match:
                result['read_iops'] = parse_iops(read_match.group(1))
                result['read_bw'] = float(read_match.group(2))
            
            if write_match:
                result['write_iops'] = parse_iops(write_match.group(1))
                result['write_bw'] = float(write_match.group(2))
                
            return result
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return {}
    
    def collect_performance_data(self):
        """收集两个测试目录的性能数据"""
        test_types = ['mixed_rw_4k', 'mixed_rw_128k', 'mixed_rw_1M']
        
        for test_type in test_types:
            baseline_file = self.baseline_path / f"{test_type}_summary.txt"
            test_file = self.test_path / f"{test_type}_summary.txt"
            
            if baseline_file.exists() and test_file.exists():
                baseline_data = self.parse_summary_file(baseline_file)
                test_data = self.parse_summary_file(test_file)
                
                self.results[test_type] = {
                    'baseline': baseline_data,
                    'test': test_data
                }
    
    def calculate_performance_ratio(self):
        """计算性能比率和Pass/Fail状态"""
        comparison_results = {}
        
        for test_type, data in self.results.items():
            baseline = data['baseline']
            test = data['test']
            
            comparison = {}
            
            # 计算读性能比率
            if 'read_iops' in baseline and 'read_iops' in test:
                read_iops_ratio = test['read_iops'] / baseline['read_iops']
                read_bw_ratio = test['read_bw'] / baseline['read_bw']
                
                comparison['read_iops_ratio'] = read_iops_ratio
                comparison['read_bw_ratio'] = read_bw_ratio
                comparison['read_status'] = 'PASS' if read_iops_ratio >= 1.0 else 'FAIL'
            
            # 计算写性能比率
            if 'write_iops' in baseline and 'write_iops' in test:
                write_iops_ratio = test['write_iops'] / baseline['write_iops']
                write_bw_ratio = test['write_bw'] / baseline['write_bw']
                
                comparison['write_iops_ratio'] = write_iops_ratio
                comparison['write_bw_ratio'] = write_bw_ratio
                comparison['write_status'] = 'PASS' if write_iops_ratio >= 1.0 else 'FAIL'
            
            # 整体状态
            read_pass = comparison.get('read_status') == 'PASS'
            write_pass = comparison.get('write_status') == 'PASS'
            comparison['overall_status'] = 'PASS' if read_pass and write_pass else 'FAIL'
            
            comparison_results[test_type] = comparison
            
        return comparison_results
    
    def generate_gnuplot_chart(self, comparison_results):
        """使用gnuplot生成图表"""
        # 准备数据文件
        data_file = '/check/performance_data.dat'
        with open(data_file, 'w') as f:
            f.write("# Test_Type Read_IOPS_Ratio Write_IOPS_Ratio Read_BW_Ratio Write_BW_Ratio\n")
            for test_type, comparison in comparison_results.items():
                test_name = test_type.replace('mixed_rw_', '')
                read_iops_ratio = comparison.get('read_iops_ratio', 0)
                write_iops_ratio = comparison.get('write_iops_ratio', 0)
                read_bw_ratio = comparison.get('read_bw_ratio', 0)
                write_bw_ratio = comparison.get('write_bw_ratio', 0)
                f.write(f"{test_name} {read_iops_ratio:.3f} {write_iops_ratio:.3f} {read_bw_ratio:.3f} {write_bw_ratio:.3f}\n")
        
        # 创建gnuplot脚本
        gnuplot_script = '''
set terminal png size 1200,800 font "Arial,12"
set output "/check/performance_comparison_chart.png"

set title "FIO Performance Comparison (Test2 vs Test1 Baseline)" font "Arial,16"
set xlabel "Test Types" font "Arial,14"
set ylabel "Performance Ratio" font "Arial,14"

set style data histograms
set style histogram clustered gap 1
set style fill solid border -1
set boxwidth 0.8

set xtics rotate by -45
set grid ytics
set yrange [0:1.2]

# Add baseline reference line
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2

set key outside right top

plot "/check/performance_data.dat" using 2:xtic(1) title "Read IOPS" lc rgb "blue", \\
     "" using 3 title "Write IOPS" lc rgb "green", \\
     "" using 4 title "Read Bandwidth" lc rgb "cyan", \\
     "" using 5 title "Write Bandwidth" lc rgb "orange"
'''
        
        # 写入gnuplot脚本文件
        script_file = '/check/plot_script.gp'
        with open(script_file, 'w') as f:
            f.write(gnuplot_script)
        
        # 执行gnuplot
        try:
            result = subprocess.run(['gnuplot', script_file], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"\n图表已生成: /check/performance_comparison_chart.png")
                return True
            else:
                print(f"Gnuplot error: {result.stderr}")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("Gnuplot not available, creating alternative chart...")
            return self.create_html_chart(comparison_results)
    
    def create_html_chart(self, comparison_results):
        """创建HTML图表作为备选方案"""
        html_content = '''
<!DOCTYPE html>
<html>
<head>
    <title>Performance Comparison Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { width: 80%; margin: 20px auto; }
        h1 { text-align: center; color: #333; }
    </style>
</head>
<body>
    <h1>FIO Performance Comparison Chart</h1>
    <div class="chart-container">
        <canvas id="performanceChart"></canvas>
    </div>
    
    <script>
        const ctx = document.getElementById('performanceChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['4K', '128K', '1M'],
                datasets: [
                    {
                        label: 'Read IOPS Ratio',
                        data: [''' + f"{comparison_results['mixed_rw_4k']['read_iops_ratio']:.3f}, " + \
                              f"{comparison_results['mixed_rw_128k']['read_iops_ratio']:.3f}, " + \
                              f"{comparison_results['mixed_rw_1M']['read_iops_ratio']:.3f}" + '''],
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Write IOPS Ratio',
                        data: [''' + f"{comparison_results['mixed_rw_4k']['write_iops_ratio']:.3f}, " + \
                              f"{comparison_results['mixed_rw_128k']['write_iops_ratio']:.3f}, " + \
                              f"{comparison_results['mixed_rw_1M']['write_iops_ratio']:.3f}" + '''],
                        backgroundColor: 'rgba(75, 192, 192, 0.8)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Performance Ratio (Test2/Test1 Baseline)'
                    },
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 1.2,
                        ticks: {
                            callback: function(value) {
                                return value.toFixed(2);
                            }
                        },
                        grid: {
                            color: function(context) {
                                if (context.tick.value === 1.0) {
                                    return 'red';
                                }
                                return 'rgba(0, 0, 0, 0.1)';
                            },
                            lineWidth: function(context) {
                                if (context.tick.value === 1.0) {
                                    return 3;
                                }
                                return 1;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>'''
        
        with open('/check/performance_chart.html', 'w') as f:
            f.write(html_content)
        
        print(f"\nHTML图表已生成: /check/performance_chart.html")
        return True
    
    def generate_comparison_report(self):
        """生成详细的对比报告"""
        comparison_results = self.calculate_performance_ratio()
        
        print("=" * 80)
        print("FIO 性能对比报告")
        print("=" * 80)
        print(f"基准测试 (测试1): {self.baseline_path}")
        print(f"对比测试 (测试2): {self.test_path}")
        print("=" * 80)
        
        for test_type, comparison in comparison_results.items():
            baseline_data = self.results[test_type]['baseline']
            test_data = self.results[test_type]['test']
            
            print(f"\n【{test_type.upper()}】")
            print("-" * 50)
            
            # 读性能对比
            if 'read_iops_ratio' in comparison:
                print(f"读性能 (Read Performance):")
                print(f"  IOPS: {baseline_data['read_iops']:,.0f} → {test_data['read_iops']:,.0f} "
                      f"(比率: {comparison['read_iops_ratio']:.3f}) [{comparison['read_status']}]")
                print(f"  带宽: {baseline_data['read_bw']:.1f} MiB/s → {test_data['read_bw']:.1f} MiB/s "
                      f"(比率: {comparison['read_bw_ratio']:.3f})")
            
            # 写性能对比
            if 'write_iops_ratio' in comparison:
                print(f"写性能 (Write Performance):")
                print(f"  IOPS: {baseline_data['write_iops']:,.0f} → {test_data['write_iops']:,.0f} "
                      f"(比率: {comparison['write_iops_ratio']:.3f}) [{comparison['write_status']}]")
                print(f"  带宽: {baseline_data['write_bw']:.1f} MiB/s → {test_data['write_bw']:.1f} MiB/s "
                      f"(比率: {comparison['write_bw_ratio']:.3f})")
            
            print(f"整体状态: {comparison['overall_status']}")
        
        # 生成汇总表
        print("\n" + "=" * 80)
        print("性能对比汇总表")
        print("=" * 80)
        print(f"{'测试项目':<15} {'读IOPS比率':<12} {'读状态':<8} {'写IOPS比率':<12} {'写状态':<8} {'整体状态':<8}")
        print("-" * 80)
        
        for test_type, comparison in comparison_results.items():
            read_ratio = comparison.get('read_iops_ratio', 0)
            write_ratio = comparison.get('write_iops_ratio', 0)
            read_status = comparison.get('read_status', 'N/A')
            write_status = comparison.get('write_status', 'N/A')
            overall_status = comparison.get('overall_status', 'N/A')
            
            print(f"{test_type:<15} {read_ratio:<12.3f} {read_status:<8} {write_ratio:<12.3f} {write_status:<8} {overall_status:<8}")
        
        return comparison_results

def main():
    # 设置测试路径
    baseline_path = "/check/fio-web-controller/hp"
    test_path = "/check/fio_results_20250716_121731"
    
    # 创建比较器
    comparator = FIOPerformanceComparator(baseline_path, test_path)
    
    # 收集数据
    comparator.collect_performance_data()
    
    # 生成报告
    comparison_results = comparator.generate_comparison_report()
    
    # 生成图表
    comparator.generate_gnuplot_chart(comparison_results)

if __name__ == "__main__":
    main()
