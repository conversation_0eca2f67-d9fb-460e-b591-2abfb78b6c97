
set terminal png size 1400,900 font "Arial,14"
set output "/check/performance_comparison_chart.png"

set multiplot layout 2,2 title "FIO Performance Comparison (Test2 vs Test1)" font "Arial,18"

# IOPS Comparison
set title "IOPS Performance Ratio" font "Arial,16"
set xlabel "Test Types"
set ylabel "Performance Ratio"
set style data histograms
set style histogram clustered gap 1
set style fill solid border -1
set boxwidth 0.8
set xtics rotate by -45
set grid ytics
set yrange [0.9:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
plot "/check/performance_data.dat" using 2:xtic(1) title "Read IOPS" lc rgb "blue", \
     "" using 3 title "Write IOPS" lc rgb "green"

# Bandwidth Comparison  
set title "Bandwidth Performance Ratio" font "Arial,16"
set xlabel "Test Types"
set ylabel "Performance Ratio"
set yrange [0.9:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
plot "/check/performance_data.dat" using 4:xtic(1) title "Read BW" lc rgb "cyan", \
     "" using 5 title "Write BW" lc rgb "orange"

# Performance Summary
set title "Overall Performance Status" font "Arial,16"
set xlabel "Test Types"
set ylabel "Pass/Fail Status"
set yrange [0:2]
set ytics ("FAIL" 0, "PASS" 1)
set style fill solid
plot "/check/performance_data.dat" using ($2>=1.0 && $3>=1.0 ? 1 : 0):xtic(1) with boxes title "Overall Status" lc variable

# Detailed Numbers
set title "Performance Ratios (Numerical)" font "Arial,16"
set xlabel "Metrics"
set ylabel "Ratio Values"
set xtics rotate by -45
set yrange [0.95:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
set style data linespoints
plot "/check/performance_data.dat" using 2 title "4K Read" with linespoints pt 7 ps 1.5, \
     "" using 3 title "4K Write" with linespoints pt 7 ps 1.5, \
     "" using 4 title "128K Read" with linespoints pt 7 ps 1.5, \
     "" using 5 title "128K Write" with linespoints pt 7 ps 1.5

unset multiplot
