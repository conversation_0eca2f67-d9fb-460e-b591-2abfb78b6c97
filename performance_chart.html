
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO Performance Comparison Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 { 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .chart-container { 
            position: relative; 
            height: 500px; 
            margin: 30px 0; 
        }
        .summary { 
            display: flex; 
            justify-content: space-around; 
            margin: 20px 0; 
            padding: 20px; 
            background-color: #ecf0f1; 
            border-radius: 8px;
        }
        .metric { 
            text-align: center; 
            padding: 15px;
        }
        .metric-value { 
            font-size: 24px; 
            font-weight: bold; 
            color: #2c3e50;
        }
        .metric-label { 
            font-size: 14px; 
            color: #7f8c8d; 
            margin-top: 5px;
        }
        .status-pass { color: #27ae60; }
        .status-fail { color: #e74c3c; }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .data-table th {
            background-color: #3498db;
            color: white;
        }
        .data-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 FIO 硬盘性能对比图表</h1>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value">2/3</div>
                <div class="metric-label">测试通过</div>
            </div>
            <div class="metric">
                <div class="metric-value">66.7%</div>
                <div class="metric-label">通过率</div>
            </div>
            <div class="metric">
                <div class="metric-value">1.040</div>
                <div class="metric-label">平均性能比率</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
        
        <h2>📋 详细数据表</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>测试类型</th>
                    <th>读IOPS (基准)</th>
                    <th>读IOPS (测试)</th>
                    <th>读比率</th>
                    <th>写IOPS (基准)</th>
                    <th>写IOPS (测试)</th>
                    <th>写比率</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>4k</td>
                    <td>165,000</td>
                    <td>163,000</td>
                    <td>0.988</td>
                    <td>41,400</td>
                    <td>40,800</td>
                    <td>0.986</td>
                    <td><span class="status-fail">FAIL</span></td>
                </tr>
                <tr>
                    <td>128k</td>
                    <td>5,858</td>
                    <td>6,134</td>
                    <td>1.047</td>
                    <td>1,464</td>
                    <td>1,534</td>
                    <td>1.048</td>
                    <td><span class="status-pass">PASS</span></td>
                </tr>
                <tr>
                    <td>1M</td>
                    <td>692</td>
                    <td>750</td>
                    <td>1.084</td>
                    <td>173</td>
                    <td>187</td>
                    <td>1.081</td>
                    <td><span class="status-pass">PASS</span></td>
                </tr>
            </tbody>
        </table>
        
        <script>
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['4k', '128k', '1M'],
                    datasets: [
                        {
                            label: '读 IOPS 比率',
                            data: [0.9878787878787879, 1.0471150563332194, 1.083815028901734],
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2
                        },
                        {
                            label: '写 IOPS 比率',
                            data: [0.9855072463768116, 1.0478142076502732, 1.0809248554913296],
                            backgroundColor: 'rgba(75, 192, 192, 0.8)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '性能比率对比 (测试2 / 测试1基准)',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 1.2,
                            ticks: {
                                callback: function(value) {
                                    return value.toFixed(2);
                                }
                            },
                            grid: {
                                color: function(context) {
                                    if (Math.abs(context.tick.value - 1.0) < 0.001) {
                                        return 'red';
                                    }
                                    return 'rgba(0, 0, 0, 0.1)';
                                },
                                lineWidth: function(context) {
                                    if (Math.abs(context.tick.value - 1.0) < 0.001) {
                                        return 3;
                                    }
                                    return 1;
                                }
                            }
                        }
                    }
                }
            });
        </script>
        
        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>📈 红线表示基准线 (比率 = 1.0)</p>
            <p>🟢 绿色区域表示性能提升，🔴 红色区域表示性能下降</p>
            <p>生成时间: <script>document.write(new Date().toLocaleString('zh-CN'));</script></p>
        </div>
    </div>
</body>
</html>