#!/bin/bash

# 简化版改进图表生成器

RESULT_DIR="$1"
BLOCK_SIZES=("4k" "128k" "1M")

if [ -z "$RESULT_DIR" ]; then
    echo "用法: $0 <测试结果目录>"
    exit 1
fi

echo "=== 简化版改进图表生成器 ==="
echo "处理测试结果目录: $RESULT_DIR"
echo

# 函数：处理单个块大小的数据
process_block_size() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    
    echo "处理块大小: $block_size"
    
    # 生成带宽图表
    local bw_chart="${log_prefix}_improved_bw_chart.png"
    local bw_data="${log_prefix}_bw_combined.dat"
    
    # 合并带宽数据
    echo "# Time(s) Read_BW(MB/s) Write_BW(MB/s)" > "$bw_data"
    
    # 使用awk处理所有bw日志文件
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_bw.*.log" | sort -V | while read logfile; do
        awk -F',' '
        {
            if (NF >= 4) {
                time = $1/1000
                bw = $2/1024
                direction = $3
                if (direction == 0) {
                    read_bw[time] += bw
                } else {
                    write_bw[time] += bw
                }
            }
        }
        END {
            for (t in read_bw) {
                print t, read_bw[t], (write_bw[t] ? write_bw[t] : 0)
            }
        }' "$logfile"
    done | sort -n >> "$bw_data"
    
    # 生成带宽图表
    gnuplot << EOF
        set terminal pngcairo size 1400,800 font 'Arial,12'
        set output '$bw_chart'
        
        set title 'NVMe 性能测试 - 带宽 (块大小: ${block_size})' font 'Arial,16'
        set xlabel '时间 (秒)' font 'Arial,14'
        set ylabel '带宽 (MB/s)' font 'Arial,14'
        
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#E0E0E0'
        
        set key top right
        set key box
        set key font 'Arial,12'
        
        set xtics font 'Arial,11'
        set ytics font 'Arial,11'
        set border linewidth 2
        
        plot '$bw_data' using 1:2 with lines linewidth 2 linecolor rgb '#2E86AB' title '读取带宽 (MB/s)', \\
             '$bw_data' using 1:3 with lines linewidth 2 linecolor rgb '#A23B72' title '写入带宽 (MB/s)'
EOF
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成带宽图表: $bw_chart"
    else
        echo "✗ 生成带宽图表失败"
    fi
    
    # 生成IOPS图表
    local iops_chart="${log_prefix}_improved_iops_chart.png"
    local iops_data="${log_prefix}_iops_combined.dat"
    
    # 合并IOPS数据
    echo "# Time(s) Read_IOPS Write_IOPS" > "$iops_data"
    
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_iops.*.log" | sort -V | while read logfile; do
        awk -F',' '
        {
            if (NF >= 4) {
                time = $1/1000
                iops = $2
                direction = $3
                if (direction == 0) {
                    read_iops[time] += iops
                } else {
                    write_iops[time] += iops
                }
            }
        }
        END {
            for (t in read_iops) {
                print t, read_iops[t], (write_iops[t] ? write_iops[t] : 0)
            }
        }' "$logfile"
    done | sort -n >> "$iops_data"
    
    # 生成IOPS图表
    gnuplot << EOF
        set terminal pngcairo size 1400,800 font 'Arial,12'
        set output '$iops_chart'
        
        set title 'NVMe 性能测试 - IOPS (块大小: ${block_size})' font 'Arial,16'
        set xlabel '时间 (秒)' font 'Arial,14'
        set ylabel 'IOPS' font 'Arial,14'
        
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#E0E0E0'
        
        set key top right
        set key box
        set key font 'Arial,12'
        
        set xtics font 'Arial,11'
        set ytics font 'Arial,11'
        set border linewidth 2
        
        plot '$iops_data' using 1:2 with lines linewidth 2 linecolor rgb '#2E86AB' title '读取 IOPS', \\
             '$iops_data' using 1:3 with lines linewidth 2 linecolor rgb '#A23B72' title '写入 IOPS'
EOF
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成IOPS图表: $iops_chart"
    else
        echo "✗ 生成IOPS图表失败"
    fi
    
    echo
}

# 生成汇总对比图表
generate_summary() {
    local summary_file="$RESULT_DIR/performance_summary.dat"
    local chart_file="$RESULT_DIR/performance_summary_chart.png"
    
    echo "生成性能汇总对比图表..."
    
    # 创建汇总数据文件
    echo "# Block_Size Read_BW Write_BW Read_IOPS Write_IOPS" > "$summary_file"
    
    for bs in "${BLOCK_SIZES[@]}"; do
        local summary_txt="$RESULT_DIR/mixed_rw_${bs}_summary.txt"
        if [ -f "$summary_txt" ]; then
            # 解析汇总文件获取性能数据
            local read_bw=$(grep "read:" "$summary_txt" | grep "BW=" | sed 's/.*BW=\([0-9.]*\)MiB\/s.*/\1/' | head -1)
            local write_bw=$(grep "write:" "$summary_txt" | grep "BW=" | sed 's/.*BW=\([0-9.]*\)MiB\/s.*/\1/' | head -1)
            local read_iops=$(grep "read:" "$summary_txt" | grep "IOPS=" | sed 's/.*IOPS=\([0-9.]*\)k.*/\1/' | head -1)
            local write_iops=$(grep "write:" "$summary_txt" | grep "IOPS=" | sed 's/.*IOPS=\([0-9.]*\)k.*/\1/' | head -1)
            
            echo "$bs ${read_bw:-0} ${write_bw:-0} ${read_iops:-0} ${write_iops:-0}" >> "$summary_file"
        fi
    done
    
    # 生成汇总对比图表
    gnuplot << EOF
        set terminal pngcairo size 1600,1000 font 'Arial,12'
        set output '$chart_file'
        
        set multiplot layout 2,2 title 'NVMe 性能测试汇总对比' font 'Arial,18'
        
        # 读取带宽对比
        set title '读取带宽对比' font 'Arial,14'
        set xlabel '块大小' font 'Arial,12'
        set ylabel '带宽 (MB/s)' font 'Arial,12'
        set style fill solid 0.7
        set boxwidth 0.6
        plot '$summary_file' using 2:xtic(1) with boxes linecolor rgb '#2E86AB' notitle
        
        # 写入带宽对比
        set title '写入带宽对比' font 'Arial,14'
        set xlabel '块大小' font 'Arial,12'
        set ylabel '带宽 (MB/s)' font 'Arial,12'
        plot '$summary_file' using 3:xtic(1) with boxes linecolor rgb '#A23B72' notitle
        
        # 读取IOPS对比
        set title '读取IOPS对比' font 'Arial,14'
        set xlabel '块大小' font 'Arial,12'
        set ylabel 'IOPS (K)' font 'Arial,12'
        plot '$summary_file' using 4:xtic(1) with boxes linecolor rgb '#2E86AB' notitle
        
        # 写入IOPS对比
        set title '写入IOPS对比' font 'Arial,14'
        set xlabel '块大小' font 'Arial,12'
        set ylabel 'IOPS (K)' font 'Arial,12'
        plot '$summary_file' using 5:xtic(1) with boxes linecolor rgb '#A23B72' notitle
        
        unset multiplot
EOF
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成汇总图表: $chart_file"
    else
        echo "✗ 生成汇总图表失败"
    fi
}

# 主程序
for bs in "${BLOCK_SIZES[@]}"; do
    process_block_size "$bs"
done

generate_summary

echo "图表生成完成！"
echo
echo "生成的图表文件："
find "$RESULT_DIR" -name "*improved*chart.png" -o -name "*summary*chart.png" | sort
