# FIO 趋势线图表说明

## 概述

根据您的需求，我们创建了专门的趋势线图表生成器，能够清晰地显示FIO测试中每5秒采样的性能数据趋势。

## 核心特点

### 📊 数据采样
- **采样间隔**: 每5秒记录一次性能数据点
- **数据来源**: 合并所有job的性能数据，获得更准确的整体性能
- **时间轴**: 完整的测试时间线，从开始到结束

### 📈 趋势线显示
- **连续线条**: 用线条连接每个5秒间隔的数据点
- **清晰趋势**: 能够清楚看出性能随时间的变化趋势
- **双重视角**: 同时提供带宽和IOPS两种性能指标

## 生成的图表类型

### 1. 带宽趋势图
- **文件名**: `mixed_rw_*_bandwidth_trend.png`
- **内容**: 显示读取和写入带宽随时间的变化
- **Y轴**: 带宽 (MB/s)
- **X轴**: 时间 (秒)

### 2. IOPS趋势图
- **文件名**: `mixed_rw_*_iops_trend.png`
- **内容**: 显示读取和写入IOPS随时间的变化
- **Y轴**: IOPS (每秒I/O操作数)
- **X轴**: 时间 (秒)

### 3. 综合对比图
- **文件名**: `performance_trends_combined.png`
- **内容**: 不同块大小的性能趋势对比
- **布局**: 上下分布，分别显示读取和写入趋势

## 使用方法

### 自动生成（推荐）
```bash
# Web界面测试 - 自动生成趋势图
./start_simple.sh

# 命令行测试 - 自动生成趋势图
./test_nvme_cdn.sh
```

### 手动生成
```bash
# 对现有测试结果生成趋势图
./generate_trend_charts.sh test_results

# 查看生成的趋势图
ls test_results/*trend*.png
```

### 效果演示
```bash
# 运行演示脚本，对比原始图表和趋势图
./demo_trend_charts.sh
```

## 数据示例

### 原始数据格式
```
5000, 25226, 0, 0    # 5秒时，读取带宽25226 KB/s
5000, 6414, 1, 0     # 5秒时，写入带宽6414 KB/s
10000, 25224, 0, 0   # 10秒时，读取带宽25224 KB/s
10000, 6324, 1, 0    # 10秒时，写入带宽6324 KB/s
```

### 处理后的趋势数据
```
# Time(s) Read_BW(MB/s) Write_BW(MB/s)
5 1432.83 359.686
10 888.958 228.613
15 419.048 104.771
20 868.394 216.953
```

## 趋势分析价值

### 🔍 性能稳定性分析
- 观察性能线条是否平稳
- 识别性能波动的幅度和频率
- 评估系统的稳定性表现

### 📊 预热效应观察
- 查看测试初期的性能爬升
- 确定系统达到稳定状态的时间
- 分析预热阶段的性能特征

### 📉 性能衰减检测
- 监控长时间测试中的性能下降
- 识别可能的热节流或磨损影响
- 评估持续负载下的性能表现

### ⚡ 异常点识别
- 发现性能突变或异常峰值
- 定位可能的系统干扰时间点
- 分析异常的持续时间和影响

## 图表特色

### 🎨 专业样式
- 现代化的图表设计
- 清晰的网格线和标签
- 专业的配色方案
- 高分辨率输出 (1600x900)

### 📏 精确刻度
- X轴：时间刻度，单位为秒
- Y轴：性能刻度，自动调整范围
- 图例：清晰标注读取和写入

### 🔗 数据连续性
- 每个数据点用线条连接
- 形成连续的趋势线
- 便于观察性能变化模式

## 实际测试数据统计

基于示例测试结果：
- **4K块大小**: 1438个数据点，测试时长3600秒
- **128K块大小**: 7593个数据点，测试时长3600秒  
- **1M块大小**: 7292个数据点，测试时长3600秒

每个数据点代表5秒间隔内的平均性能，连接起来形成清晰的性能趋势线。

## 与原始图表的对比

### 原始图表问题
- 只显示单个job的数据
- 数据点密集，难以看清趋势
- 缺乏整体性能视角

### 趋势图表优势
- ✅ 合并所有job数据，更准确
- ✅ 清晰的趋势线，易于分析
- ✅ 专业的图表样式
- ✅ 多维度性能对比
- ✅ 每5秒采样，趋势明显

## 总结

新的趋势线图表完全符合您的需求：
1. **每5秒采样一次**：精确按照FIO的日志间隔
2. **线条连接**：所有数据点用线条连接形成趋势线
3. **清晰趋势**：能够清楚看出性能随时间的变化
4. **专业呈现**：现代化的图表样式，适合分析和报告

这样的图表能够帮助您更好地理解NVMe硬盘的性能特征，识别性能模式，并进行深入的性能分析。
