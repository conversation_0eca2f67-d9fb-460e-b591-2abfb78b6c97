#!/bin/bash

# FIO 趋势线图表效果演示脚本

RESULT_DIR="test_results"

echo "=== FIO 趋势线图表效果演示 ==="
echo

if [ ! -d "$RESULT_DIR" ]; then
    echo "错误: 测试结果目录 $RESULT_DIR 不存在"
    echo "请先运行 FIO 测试或复制现有测试结果"
    exit 1
fi

echo "📊 测试数据概览:"
echo "测试结果目录: $RESULT_DIR"
echo "数据采样间隔: 5秒 (5000毫秒)"
echo

# 分析数据文件
echo "📈 数据文件分析:"
for bs in "4k" "128k" "1M"; do
    bw_files=$(find "$RESULT_DIR" -name "mixed_rw_${bs}_bw.*.log" | wc -l)
    iops_files=$(find "$RESULT_DIR" -name "mixed_rw_${bs}_iops.*.log" | wc -l)
    
    if [ $bw_files -gt 0 ]; then
        # 分析第一个文件的数据点数量和时间范围
        first_file=$(find "$RESULT_DIR" -name "mixed_rw_${bs}_bw.*.log" | head -1)
        data_points=$(wc -l < "$first_file")
        first_time=$(head -1 "$first_file" | cut -d',' -f1)
        last_time=$(tail -1 "$first_file" | cut -d',' -f1)
        duration=$((($last_time - $first_time) / 1000))
        
        echo "  ${bs} 块大小: ${bw_files} 个job, 每个job ${data_points} 个数据点, 测试时长 ${duration} 秒"
    fi
done

echo

# 生成原始风格图表（用于对比）
echo "1️⃣ 生成原始风格图表（单job数据）..."
for bs in "4k" "128k" "1M"; do
    LOG_PREFIX="$RESULT_DIR/mixed_rw_${bs}"
    CHART_FILE="${LOG_PREFIX}_original_trend.png"
    SOURCE_LOG_FILE="${LOG_PREFIX}_bw.1.log"
    
    if [ -f "${SOURCE_LOG_FILE}" ]; then
        gnuplot << EOF
            set terminal pngcairo size 1200,600 font 'Verdana,10'
            set output '${CHART_FILE}'
            set title 'NVMe Performance Trend (${bs}, Single Job) - Original Style'
            set xlabel 'Time (seconds)'
            set ylabel 'Bandwidth (MB/s)'
            set grid
            set key top right
            set format x '%g'
            
            plot '${SOURCE_LOG_FILE}' using (\$1/1000):(\$2/1024) with lines title 'Read BW (MB/s)', \\
                 '${SOURCE_LOG_FILE}' using (\$1/1000):(\$3/1024) with lines title 'Write BW (MB/s)'
EOF
        echo "  ✓ 生成原始趋势图: $(basename "${CHART_FILE}")"
    fi
done

echo

# 生成改进的趋势线图表
echo "2️⃣ 生成改进的趋势线图表（多job合并）..."
./generate_trend_charts.sh "$RESULT_DIR"

echo

# 显示对比结果
echo "3️⃣ 图表对比结果:"
echo
echo "📊 原始图表特点 (单job数据):"
find "$RESULT_DIR" -name "*original_trend.png" | sort | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  - $(basename "$file") ($size)"
done

echo
echo "📈 改进趋势图表特点 (多job合并):"
find "$RESULT_DIR" -name "*trend.png" | grep -v original | sort | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  - $(basename "$file") ($size)"
done

echo
echo "=== 趋势线图表特点说明 ==="
echo
echo "🎯 数据采样特点:"
echo "  - 采样间隔: 每5秒记录一次性能数据"
echo "  - 数据来源: 合并所有job的性能数据"
echo "  - 趋势显示: 清晰的线条连接每个数据点"
echo "  - 时间轴: 从测试开始到结束的完整时间线"
echo
echo "📊 图表类型:"
echo "  - 带宽趋势图: 显示读写带宽随时间的变化"
echo "  - IOPS趋势图: 显示读写IOPS随时间的变化"
echo "  - 综合对比图: 不同块大小的性能趋势对比"
echo
echo "🔍 趋势分析价值:"
echo "  - 性能稳定性: 观察性能是否稳定或有波动"
echo "  - 预热效应: 查看测试初期的性能爬升"
echo "  - 性能衰减: 检测长时间测试中的性能下降"
echo "  - 异常检测: 识别性能突变或异常点"
echo
echo "💡 使用建议:"
echo "  - 关注趋势线的整体走向而非单个数据点"
echo "  - 对比不同块大小的性能趋势差异"
echo "  - 结合带宽和IOPS图表进行综合分析"
echo "  - 注意测试初期和稳定期的性能差异"
echo

# 显示数据统计信息
echo "📊 数据统计信息:"
for bs in "4k" "128k" "1M"; do
    trend_file="$RESULT_DIR/mixed_rw_${bs}_bandwidth_trend.dat"
    if [ -f "$trend_file" ]; then
        data_points=$(( $(wc -l < "$trend_file") - 1 ))
        duration=$(tail -1 "$trend_file" | awk '{print int($1)}')
        echo "  ${bs} 块大小: ${data_points} 个数据点, 测试时长 ${duration} 秒"
    fi
done

echo
echo "🎨 查看生成的图表文件:"
echo "  ls $RESULT_DIR/*trend*.png"
echo
echo "✨ 趋势线图表生成完成！每5秒一个数据点，清晰显示性能变化趋势。"
