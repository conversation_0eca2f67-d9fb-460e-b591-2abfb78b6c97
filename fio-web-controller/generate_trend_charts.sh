#!/bin/bash

# FIO 趋势线图表生成器
# 专门用于生成清晰的性能趋势线图表，每5秒采样一次

RESULT_DIR="$1"
BLOCK_SIZES=("4k" "128k" "1M")

if [ -z "$RESULT_DIR" ]; then
    echo "用法: $0 <测试结果目录>"
    exit 1
fi

if [ ! -d "$RESULT_DIR" ]; then
    echo "错误: 结果目录 $RESULT_DIR 不存在"
    exit 1
fi

if ! command -v gnuplot >/dev/null 2>&1; then
    echo "错误: 未找到 gnuplot，请先安装"
    echo "安装命令: sudo apt install gnuplot"
    exit 1
fi

echo "=== FIO 趋势线图表生成器 ==="
echo "处理测试结果目录: $RESULT_DIR"
echo "数据采样间隔: 5秒"
echo

# 函数：处理单个块大小的带宽趋势图
generate_bandwidth_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_bandwidth_trend.png"
    local data_file="${log_prefix}_bandwidth_trend.dat"
    
    echo "生成 ${block_size} 带宽趋势图..."
    
    # 创建数据文件头部
    echo "# Time(s) Read_BW(MB/s) Write_BW(MB/s)" > "$data_file"
    
    # 合并所有job的带宽数据，按时间戳聚合
    local temp_file=$(mktemp)
    
    # 收集所有带宽日志文件
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_bw.*.log" | sort -V | while read logfile; do
        cat "$logfile"
    done | awk -F',' '
    {
        if (NF >= 4) {
            timestamp = $1
            bandwidth = $2  # KB/s
            direction = $3  # 0=read, 1=write
            
            if (direction == 0) {
                read_bw[timestamp] += bandwidth
            } else {
                write_bw[timestamp] += bandwidth
            }
        }
    }
    END {
        # 按时间戳排序输出
        for (ts in read_bw) {
            time_sec = ts / 1000
            read_mb = read_bw[ts] / 1024
            write_mb = (write_bw[ts] ? write_bw[ts] : 0) / 1024
            print time_sec, read_mb, write_mb
        }
    }' | sort -n >> "$data_file"
    
    # 检查数据文件是否有内容
    if [ $(wc -l < "$data_file") -le 1 ]; then
        echo "警告: 没有找到有效的带宽数据"
        return 1
    fi
    
    # 生成趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1600,900 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe 带宽性能趋势 (块大小: ${block_size}, 采样间隔: 5秒)' font 'Arial,18' offset 0,-1
        set xlabel '时间 (秒)' font 'Arial,16'
        set ylabel '带宽 (MB/s)' font 'Arial,16'
        
        # 网格设置
        set grid xtics ytics
        set grid linewidth 1.5 linetype 0 linecolor rgb '#D0D0D0'
        
        # 图例设置
        set key top right
        set key box linewidth 2
        set key font 'Arial,14'
        set key spacing 1.2
        
        # 坐标轴设置
        set xtics font 'Arial,12'
        set ytics font 'Arial,12'
        set border linewidth 2
        
        # 设置线条样式
        set style line 1 linecolor rgb '#1f77b4' linewidth 3 pointtype 7 pointsize 0.8
        set style line 2 linecolor rgb '#ff7f0e' linewidth 3 pointtype 7 pointsize 0.8
        
        # 设置填充样式（可选）
        set style fill transparent solid 0.1
        
        # 绘制趋势线
        plot '$data_file' using 1:2 with linespoints linestyle 1 title '读取带宽', \\
             '$data_file' using 1:3 with linespoints linestyle 2 title '写入带宽'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成带宽趋势图: $chart_file"
        echo "  数据点数量: $(( $(wc -l < "$data_file") - 1 ))"
    else
        echo "✗ 生成带宽趋势图失败"
        return 1
    fi
}

# 函数：处理单个块大小的IOPS趋势图
generate_iops_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_iops_trend.png"
    local data_file="${log_prefix}_iops_trend.dat"
    
    echo "生成 ${block_size} IOPS趋势图..."
    
    # 创建数据文件头部
    echo "# Time(s) Read_IOPS Write_IOPS" > "$data_file"
    
    # 合并所有job的IOPS数据，按时间戳聚合
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_iops.*.log" | sort -V | while read logfile; do
        cat "$logfile"
    done | awk -F',' '
    {
        if (NF >= 4) {
            timestamp = $1
            iops = $2
            direction = $3  # 0=read, 1=write
            
            if (direction == 0) {
                read_iops[timestamp] += iops
            } else {
                write_iops[timestamp] += iops
            }
        }
    }
    END {
        # 按时间戳排序输出
        for (ts in read_iops) {
            time_sec = ts / 1000
            read_val = read_iops[ts]
            write_val = (write_iops[ts] ? write_iops[ts] : 0)
            print time_sec, read_val, write_val
        }
    }' | sort -n >> "$data_file"
    
    # 检查数据文件是否有内容
    if [ $(wc -l < "$data_file") -le 1 ]; then
        echo "警告: 没有找到有效的IOPS数据"
        return 1
    fi
    
    # 生成趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1600,900 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe IOPS性能趋势 (块大小: ${block_size}, 采样间隔: 5秒)' font 'Arial,18' offset 0,-1
        set xlabel '时间 (秒)' font 'Arial,16'
        set ylabel 'IOPS' font 'Arial,16'
        
        # 网格设置
        set grid xtics ytics
        set grid linewidth 1.5 linetype 0 linecolor rgb '#D0D0D0'
        
        # 图例设置
        set key top right
        set key box linewidth 2
        set key font 'Arial,14'
        set key spacing 1.2
        
        # 坐标轴设置
        set xtics font 'Arial,12'
        set ytics font 'Arial,12'
        set border linewidth 2
        
        # 设置线条样式
        set style line 1 linecolor rgb '#2ca02c' linewidth 3 pointtype 7 pointsize 0.8
        set style line 2 linecolor rgb '#d62728' linewidth 3 pointtype 7 pointsize 0.8
        
        # 绘制趋势线
        plot '$data_file' using 1:2 with linespoints linestyle 1 title '读取 IOPS', \\
             '$data_file' using 1:3 with linespoints linestyle 2 title '写入 IOPS'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成IOPS趋势图: $chart_file"
        echo "  数据点数量: $(( $(wc -l < "$data_file") - 1 ))"
    else
        echo "✗ 生成IOPS趋势图失败"
        return 1
    fi
}

# 函数：生成综合趋势对比图
generate_combined_trend() {
    local chart_file="$RESULT_DIR/performance_trends_combined.png"
    
    echo "生成综合性能趋势对比图..."
    
    # 检查是否有足够的数据文件
    local data_files=()
    for bs in "${BLOCK_SIZES[@]}"; do
        local bw_file="$RESULT_DIR/mixed_rw_${bs}_bandwidth_trend.dat"
        if [ -f "$bw_file" ] && [ $(wc -l < "$bw_file") -gt 1 ]; then
            data_files+=("$bw_file")
        fi
    done
    
    if [ ${#data_files[@]} -eq 0 ]; then
        echo "警告: 没有找到有效的趋势数据文件"
        return 1
    fi
    
    # 生成综合对比图表
    gnuplot << EOF
        set terminal pngcairo size 1800,1200 font 'Arial,12'
        set output '$chart_file'
        
        # 设置多图布局
        set multiplot layout 2,1 title 'NVMe 性能趋势综合对比 (采样间隔: 5秒)' font 'Arial,20'
        
        # 上半部分：读取带宽趋势对比
        set title '读取带宽趋势对比' font 'Arial,16'
        set xlabel '时间 (秒)' font 'Arial,14'
        set ylabel '读取带宽 (MB/s)' font 'Arial,14'
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#E0E0E0'
        set key top right font 'Arial,12'
        
        plot '$RESULT_DIR/mixed_rw_4k_bandwidth_trend.dat' using 1:2 with lines linewidth 2 linecolor rgb '#1f77b4' title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_bandwidth_trend.dat' using 1:2 with lines linewidth 2 linecolor rgb '#ff7f0e' title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_bandwidth_trend.dat' using 1:2 with lines linewidth 2 linecolor rgb '#2ca02c' title '1M'
        
        # 下半部分：写入带宽趋势对比
        set title '写入带宽趋势对比' font 'Arial,16'
        set xlabel '时间 (秒)' font 'Arial,14'
        set ylabel '写入带宽 (MB/s)' font 'Arial,14'
        
        plot '$RESULT_DIR/mixed_rw_4k_bandwidth_trend.dat' using 1:3 with lines linewidth 2 linecolor rgb '#1f77b4' title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_bandwidth_trend.dat' using 1:3 with lines linewidth 2 linecolor rgb '#ff7f0e' title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_bandwidth_trend.dat' using 1:3 with lines linewidth 2 linecolor rgb '#2ca02c' title '1M'
        
        unset multiplot
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成综合趋势对比图: $chart_file"
    else
        echo "✗ 生成综合趋势对比图失败"
    fi
}

# 主程序
echo "开始生成趋势线图表..."
echo

# 为每个块大小生成趋势图
for bs in "${BLOCK_SIZES[@]}"; do
    echo "处理块大小: $bs"
    generate_bandwidth_trend "$bs"
    generate_iops_trend "$bs"
    echo
done

# 生成综合对比图
generate_combined_trend

echo
echo "=== 趋势图表生成完成 ==="
echo
echo "生成的趋势图表文件："
find "$RESULT_DIR" -name "*trend*.png" | sort | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  📈 $(basename "$file") ($size)"
done

echo
echo "🎯 图表特点："
echo "  - 每5秒采样一次数据点"
echo "  - 清晰的趋势线显示性能变化"
echo "  - 合并所有job的数据获得准确结果"
echo "  - 专业的图表样式和布局"
