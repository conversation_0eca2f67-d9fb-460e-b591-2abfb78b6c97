#!/bin/bash

# 改进的FIO测试结果图表生成器 (使用gnuplot)
# 生成更清晰、更有意义的性能图表

RESULT_DIR="$1"
BLOCK_SIZES=("4k" "128k" "1M")

if [ -z "$RESULT_DIR" ]; then
    echo "用法: $0 <测试结果目录>"
    exit 1
fi

if [ ! -d "$RESULT_DIR" ]; then
    echo "错误: 结果目录 $RESULT_DIR 不存在"
    exit 1
fi

if ! command -v gnuplot >/dev/null 2>&1; then
    echo "错误: 未找到 gnuplot，请先安装"
    echo "安装命令: sudo apt install gnuplot"
    exit 1
fi

echo "=== 改进的FIO图表生成器 ==="
echo "处理测试结果目录: $RESULT_DIR"
echo

# 函数：合并多个job的数据
merge_job_data() {
    local prefix="$1"
    local output_file="$2"
    
    # 找到所有相关的日志文件
    local log_files=($(ls ${prefix}.*.log 2>/dev/null | sort -V))
    
    if [ ${#log_files[@]} -eq 0 ]; then
        echo "警告: 未找到 ${prefix} 的日志文件"
        return 1
    fi
    
    echo "合并 ${#log_files[@]} 个job的数据到 $output_file"
    
    # 创建临时文件来存储合并后的数据
    local temp_file=$(mktemp)
    
    # 读取所有文件并按时间戳合并
    python3 -c "
import sys
from collections import defaultdict

# 存储所有数据点
data_points = defaultdict(lambda: {'read': 0, 'write': 0})

# 读取所有日志文件
for log_file in sys.argv[1:]:
    try:
        with open(log_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and ',' in line:
                    parts = line.split(',')
                    if len(parts) >= 4:
                        timestamp = int(parts[0])
                        value = int(parts[1])
                        direction = int(parts[2])  # 0=read, 1=write
                        
                        if direction == 0:
                            data_points[timestamp]['read'] += value
                        else:
                            data_points[timestamp]['write'] += value
    except Exception as e:
        print(f'警告: 处理文件 {log_file} 时出错: {e}', file=sys.stderr)

# 输出合并后的数据
for timestamp in sorted(data_points.keys()):
    read_val = data_points[timestamp]['read']
    write_val = data_points[timestamp]['write']
    print(f'{timestamp}, {read_val}, 0, 0')
    if write_val > 0:
        print(f'{timestamp}, {write_val}, 1, 0')
" "${log_files[@]}" > "$temp_file"
    
    # 移动到目标文件
    mv "$temp_file" "$output_file"
    return 0
}

# 函数：生成带宽图表
generate_bandwidth_chart() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local merged_file="${log_prefix}_merged_bw.log"
    local chart_file="${log_prefix}_improved_bw_chart.png"
    
    echo "生成 ${block_size} 带宽图表..."
    
    # 合并多个job的数据
    if ! merge_job_data "${log_prefix}_bw" "$merged_file"; then
        return 1
    fi
    
    # 检查合并后的文件是否有数据
    if [ ! -s "$merged_file" ]; then
        echo "警告: 合并后的文件 $merged_file 为空"
        return 1
    fi
    
    # 使用gnuplot生成改进的图表
    gnuplot -e "
        set terminal pngcairo size 1400,800 font 'Arial,12';
        set output '$chart_file';
        
        # 设置标题和标签
        set title 'NVMe 性能测试 - 带宽 (块大小: ${block_size})' font 'Arial,16';
        set xlabel '时间 (秒)' font 'Arial,14';
        set ylabel '带宽 (MB/s)' font 'Arial,14';
        
        # 网格和样式
        set grid xtics ytics;
        set grid linewidth 1 linetype 0 linecolor rgb '#E0E0E0';
        
        # 图例设置
        set key top right;
        set key box;
        set key font 'Arial,12';
        
        # 坐标轴设置
        set xtics font 'Arial,11';
        set ytics font 'Arial,11';
        set format x '%g';
        set format y '%.0f';
        
        # 边框设置
        set border linewidth 2;
        
        # 数据平滑
        set samples 1000;
        
        # 绘制图表 - 分别处理读取和写入数据
        plot '$merged_file' using (\$3==0 ? \$1/1000 : 1/0):(\$3==0 ? \$2/1024 : 1/0) with lines linewidth 2 linecolor rgb '#2E86AB' title '读取带宽 (MB/s)', \\
             '$merged_file' using (\$3==1 ? \$1/1000 : 1/0):(\$3==1 ? \$2/1024 : 1/0) with lines linewidth 2 linecolor rgb '#A23B72' title '写入带宽 (MB/s)';
    "
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成带宽图表: $chart_file"
    else
        echo "✗ 生成带宽图表失败"
    fi
}

# 函数：生成IOPS图表
generate_iops_chart() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local merged_file="${log_prefix}_merged_iops.log"
    local chart_file="${log_prefix}_improved_iops_chart.png"
    
    echo "生成 ${block_size} IOPS图表..."
    
    # 合并多个job的数据
    if ! merge_job_data "${log_prefix}_iops" "$merged_file"; then
        return 1
    fi
    
    # 检查合并后的文件是否有数据
    if [ ! -s "$merged_file" ]; then
        echo "警告: 合并后的文件 $merged_file 为空"
        return 1
    fi
    
    # 使用gnuplot生成改进的图表
    gnuplot -e "
        set terminal pngcairo size 1400,800 font 'Arial,12';
        set output '$chart_file';
        
        # 设置标题和标签
        set title 'NVMe 性能测试 - IOPS (块大小: ${block_size})' font 'Arial,16';
        set xlabel '时间 (秒)' font 'Arial,14';
        set ylabel 'IOPS' font 'Arial,14';
        
        # 网格和样式
        set grid xtics ytics;
        set grid linewidth 1 linetype 0 linecolor rgb '#E0E0E0';
        
        # 图例设置
        set key top right;
        set key box;
        set key font 'Arial,12';
        
        # 坐标轴设置
        set xtics font 'Arial,11';
        set ytics font 'Arial,11';
        set format x '%g';
        set format y '%.0f';
        
        # 边框设置
        set border linewidth 2;
        
        # 数据平滑
        set samples 1000;
        
        # 绘制图表 - 分别处理读取和写入数据
        plot '$merged_file' using (\$3==0 ? \$1/1000 : 1/0):(\$3==0 ? \$2 : 1/0) with lines linewidth 2 linecolor rgb '#2E86AB' title '读取 IOPS', \\
             '$merged_file' using (\$3==1 ? \$1/1000 : 1/0):(\$3==1 ? \$2 : 1/0) with lines linewidth 2 linecolor rgb '#A23B72' title '写入 IOPS';
    "
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成IOPS图表: $chart_file"
    else
        echo "✗ 生成IOPS图表失败"
    fi
}

# 函数：生成汇总对比图表
generate_summary_chart() {
    local summary_file="$RESULT_DIR/performance_summary.txt"
    local chart_file="$RESULT_DIR/performance_summary_chart.png"
    
    echo "生成性能汇总对比图表..."
    
    # 创建汇总数据文件
    echo "# Block_Size Read_BW(MB/s) Write_BW(MB/s) Read_IOPS Write_IOPS" > "$summary_file"
    
    for bs in "${BLOCK_SIZES[@]}"; do
        local summary_txt="$RESULT_DIR/mixed_rw_${bs}_summary.txt"
        if [ -f "$summary_txt" ]; then
            # 解析汇总文件获取性能数据
            local read_bw=$(grep "read:" "$summary_txt" | grep "BW=" | sed 's/.*BW=\([0-9.]*\)MiB\/s.*/\1/' | head -1)
            local write_bw=$(grep "write:" "$summary_txt" | grep "BW=" | sed 's/.*BW=\([0-9.]*\)MiB\/s.*/\1/' | head -1)
            local read_iops=$(grep "read:" "$summary_txt" | grep "IOPS=" | sed 's/.*IOPS=\([0-9.]*\)k.*/\1/' | head -1)
            local write_iops=$(grep "write:" "$summary_txt" | grep "IOPS=" | sed 's/.*IOPS=\([0-9.]*\)k.*/\1/' | head -1)
            
            # 处理k单位的IOPS
            if [ ! -z "$read_iops" ]; then
                read_iops=$(echo "$read_iops * 1000" | bc -l 2>/dev/null || echo "$read_iops")
            fi
            if [ ! -z "$write_iops" ]; then
                write_iops=$(echo "$write_iops * 1000" | bc -l 2>/dev/null || echo "$write_iops")
            fi
            
            echo "$bs ${read_bw:-0} ${write_bw:-0} ${read_iops:-0} ${write_iops:-0}" >> "$summary_file"
        fi
    done
    
    # 生成汇总对比图表
    gnuplot -e "
        set terminal pngcairo size 1600,1000 font 'Arial,12';
        set output '$chart_file';
        
        # 设置多图布局
        set multiplot layout 2,2 title 'NVMe 性能测试汇总对比' font 'Arial,18';
        
        # 读取带宽对比
        set title '读取带宽对比' font 'Arial,14';
        set xlabel '块大小' font 'Arial,12';
        set ylabel '带宽 (MB/s)' font 'Arial,12';
        set style fill solid 0.7;
        set boxwidth 0.6;
        plot '$summary_file' using 2:xtic(1) with boxes linecolor rgb '#2E86AB' title '';
        
        # 写入带宽对比
        set title '写入带宽对比' font 'Arial,14';
        set xlabel '块大小' font 'Arial,12';
        set ylabel '带宽 (MB/s)' font 'Arial,12';
        plot '$summary_file' using 3:xtic(1) with boxes linecolor rgb '#A23B72' title '';
        
        # 读取IOPS对比
        set title '读取IOPS对比' font 'Arial,14';
        set xlabel '块大小' font 'Arial,12';
        set ylabel 'IOPS (K)' font 'Arial,12';
        plot '$summary_file' using (\$4/1000):xtic(1) with boxes linecolor rgb '#2E86AB' title '';
        
        # 写入IOPS对比
        set title '写入IOPS对比' font 'Arial,14';
        set xlabel '块大小' font 'Arial,12';
        set ylabel 'IOPS (K)' font 'Arial,12';
        plot '$summary_file' using (\$5/1000):xtic(1) with boxes linecolor rgb '#A23B72' title '';
        
        unset multiplot;
    "
    
    if [ $? -eq 0 ]; then
        echo "✓ 生成汇总图表: $chart_file"
    else
        echo "✗ 生成汇总图表失败"
    fi
}

# 主程序
echo "开始生成改进的图表..."
echo

# 为每个块大小生成图表
for bs in "${BLOCK_SIZES[@]}"; do
    echo "处理块大小: $bs"
    generate_bandwidth_chart "$bs"
    generate_iops_chart "$bs"
    echo
done

# 生成汇总图表
generate_summary_chart

echo "图表生成完成！"
echo
echo "生成的图表文件："
find "$RESULT_DIR" -name "*improved*chart.png" -o -name "*summary*chart.png" | sort
