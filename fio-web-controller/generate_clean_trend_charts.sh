#!/bin/bash

# FIO 简洁趋势线图表生成器
# 生成类似您期望的简洁清晰的趋势线图表

RESULT_DIR="$1"
BLOCK_SIZES=("4k" "128k" "1M")

if [ -z "$RESULT_DIR" ]; then
    echo "用法: $0 <测试结果目录>"
    exit 1
fi

if [ ! -d "$RESULT_DIR" ]; then
    echo "错误: 结果目录 $RESULT_DIR 不存在"
    exit 1
fi

if ! command -v gnuplot >/dev/null 2>&1; then
    echo "错误: 未找到 gnuplot，请先安装"
    exit 1
fi

echo "=== FIO 简洁趋势线图表生成器 ==="
echo "处理测试结果目录: $RESULT_DIR"
echo

# 函数：对数据进行采样和平滑处理
process_data_for_clean_trend() {
    local input_file="$1"
    local output_file="$2"
    local sample_interval="$3"  # 采样间隔（秒）
    
    # 使用awk处理数据，进行时间窗口平均
    awk -v interval="$sample_interval" '
    BEGIN {
        window_size = interval * 1000  # 转换为毫秒
    }
    {
        if (NF >= 4) {
            timestamp = $1
            value = $2
            direction = $3
            
            # 计算时间窗口
            window = int(timestamp / window_size) * window_size
            
            if (direction == 0) {
                read_sum[window] += value
                read_count[window]++
            } else {
                write_sum[window] += value
                write_count[window]++
            }
        }
    }
    END {
        # 输出平均值
        for (w in read_sum) {
            time_sec = w / 1000
            read_avg = read_sum[w] / read_count[w]
            write_avg = (write_sum[w] && write_count[w]) ? write_sum[w] / write_count[w] : 0
            print time_sec, read_avg, write_avg
        }
    }' "$input_file" | sort -n > "$output_file"
}

# 函数：生成简洁的带宽趋势图
generate_clean_bandwidth_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_clean_bandwidth_trend.png"
    local data_file="${log_prefix}_clean_bandwidth_trend.dat"
    
    echo "生成 ${block_size} 简洁带宽趋势图..."
    
    # 创建临时合并文件
    local temp_merged=$(mktemp)
    
    # 合并所有job的带宽数据
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_bw.*.log" | sort -V | while read logfile; do
        cat "$logfile"
    done | awk -F',' '
    {
        if (NF >= 4) {
            # 去除空格
            gsub(/[ \t]/, "", $1)
            gsub(/[ \t]/, "", $2)
            gsub(/[ \t]/, "", $3)

            timestamp = $1
            bandwidth = $2  # KB/s
            direction = $3  # 0=read, 1=write

            if (direction == 0) {
                read_bw[timestamp] += bandwidth
            } else {
                write_bw[timestamp] += bandwidth
            }
        }
    }
    END {
        for (ts in read_bw) {
            print ts "," read_bw[ts] ",0,0"
            if (write_bw[ts] > 0) {
                print ts "," write_bw[ts] ",1,0"
            }
        }
    }' > "$temp_merged"
    
    # 对数据进行采样处理（每30秒一个点，减少数据密度）
    local sample_interval=30
    process_data_for_clean_trend "$temp_merged" "$data_file.tmp" "$sample_interval"
    
    # 转换为MB/s并添加头部
    echo "# Time(s) Read_BW(MB/s) Write_BW(MB/s)" > "$data_file"
    awk '{print $1, $2/1024, $3/1024}' "$data_file.tmp" >> "$data_file"
    
    # 清理临时文件
    rm -f "$temp_merged" "$data_file.tmp"
    
    # 检查数据文件
    if [ $(wc -l < "$data_file") -le 1 ]; then
        echo "警告: 没有找到有效的带宽数据"
        return 1
    fi
    
    # 生成简洁的趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1400,800 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe 带宽性能趋势 (块大小: ${block_size})' font 'Arial,18'
        set xlabel '时间 (分钟)' font 'Arial,16'
        set ylabel '带宽 (MB/s)' font 'Arial,16'
        
        # 简洁的网格设置
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#F0F0F0'
        
        # 图例设置
        set key top right
        set key box linewidth 1
        set key font 'Arial,14'
        
        # 坐标轴设置
        set xtics font 'Arial,12'
        set ytics font 'Arial,12'
        set border linewidth 2
        
        # 转换时间轴为分钟
        set xtics format "%.0f"
        
        # 设置简洁的线条样式
        set style line 1 linecolor rgb '#1f77b4' linewidth 3 pointtype 7 pointsize 1.2
        set style line 2 linecolor rgb '#ff7f0e' linewidth 3 pointtype 7 pointsize 1.2
        
        # 绘制简洁的趋势线（带数据点）
        plot '$data_file' using (\$1/60):2 with linespoints linestyle 1 title '读取带宽', \\
             '$data_file' using (\$1/60):3 with linespoints linestyle 2 title '写入带宽'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成简洁带宽趋势图: $chart_file"
        echo "  数据点数量: $(( $(wc -l < "$data_file") - 1 ))"
    else
        echo "✗ 生成简洁带宽趋势图失败"
        return 1
    fi
}

# 函数：生成简洁的IOPS趋势图
generate_clean_iops_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_clean_iops_trend.png"
    local data_file="${log_prefix}_clean_iops_trend.dat"
    
    echo "生成 ${block_size} 简洁IOPS趋势图..."
    
    # 创建临时合并文件
    local temp_merged=$(mktemp)
    
    # 合并所有job的IOPS数据
    find "$RESULT_DIR" -name "mixed_rw_${block_size}_iops.*.log" | sort -V | while read logfile; do
        cat "$logfile"
    done | awk -F',' '
    {
        if (NF >= 4) {
            # 去除空格
            gsub(/[ \t]/, "", $1)
            gsub(/[ \t]/, "", $2)
            gsub(/[ \t]/, "", $3)

            timestamp = $1
            iops = $2
            direction = $3  # 0=read, 1=write

            if (direction == 0) {
                read_iops[timestamp] += iops
            } else {
                write_iops[timestamp] += iops
            }
        }
    }
    END {
        for (ts in read_iops) {
            print ts "," read_iops[ts] ",0,0"
            if (write_iops[ts] > 0) {
                print ts "," write_iops[ts] ",1,0"
            }
        }
    }' > "$temp_merged"
    
    # 对数据进行采样处理（每30秒一个点）
    local sample_interval=30
    process_data_for_clean_trend "$temp_merged" "$data_file.tmp" "$sample_interval"
    
    # 添加头部
    echo "# Time(s) Read_IOPS Write_IOPS" > "$data_file"
    awk '{print $1, $2, $3}' "$data_file.tmp" >> "$data_file"
    
    # 清理临时文件
    rm -f "$temp_merged" "$data_file.tmp"
    
    # 检查数据文件
    if [ $(wc -l < "$data_file") -le 1 ]; then
        echo "警告: 没有找到有效的IOPS数据"
        return 1
    fi
    
    # 生成简洁的趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1400,800 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe IOPS性能趋势 (块大小: ${block_size})' font 'Arial,18'
        set xlabel '时间 (分钟)' font 'Arial,16'
        set ylabel 'IOPS' font 'Arial,16'
        
        # 简洁的网格设置
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#F0F0F0'
        
        # 图例设置
        set key top right
        set key box linewidth 1
        set key font 'Arial,14'
        
        # 坐标轴设置
        set xtics font 'Arial,12'
        set ytics font 'Arial,12'
        set border linewidth 2
        
        # 设置简洁的线条样式
        set style line 1 linecolor rgb '#2ca02c' linewidth 3 pointtype 7 pointsize 1.2
        set style line 2 linecolor rgb '#d62728' linewidth 3 pointtype 7 pointsize 1.2
        
        # 绘制简洁的趋势线（带数据点）
        plot '$data_file' using (\$1/60):2 with linespoints linestyle 1 title '读取 IOPS', \\
             '$data_file' using (\$1/60):3 with linespoints linestyle 2 title '写入 IOPS'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成简洁IOPS趋势图: $chart_file"
        echo "  数据点数量: $(( $(wc -l < "$data_file") - 1 ))"
    else
        echo "✗ 生成简洁IOPS趋势图失败"
        return 1
    fi
}

# 函数：生成综合简洁对比图
generate_clean_combined_trend() {
    local chart_file="$RESULT_DIR/clean_performance_trends_combined.png"
    
    echo "生成综合简洁性能趋势对比图..."
    
    # 生成综合对比图表
    gnuplot << EOF
        set terminal pngcairo size 1600,1000 font 'Arial,12'
        set output '$chart_file'
        
        # 设置多图布局
        set multiplot layout 2,1 title 'NVMe 性能趋势对比 (简洁版)' font 'Arial,18'
        
        # 上半部分：读取带宽趋势对比
        set title '读取带宽趋势对比' font 'Arial,16'
        set xlabel '时间 (分钟)' font 'Arial,14'
        set ylabel '读取带宽 (MB/s)' font 'Arial,14'
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#F5F5F5'
        set key top right font 'Arial,12'
        
        set style line 1 linecolor rgb '#1f77b4' linewidth 3 pointtype 7 pointsize 1.0
        set style line 2 linecolor rgb '#ff7f0e' linewidth 3 pointtype 5 pointsize 1.0
        set style line 3 linecolor rgb '#2ca02c' linewidth 3 pointtype 9 pointsize 1.0
        
        plot '$RESULT_DIR/mixed_rw_4k_clean_bandwidth_trend.dat' using (\$1/60):2 with linespoints linestyle 1 title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_clean_bandwidth_trend.dat' using (\$1/60):2 with linespoints linestyle 2 title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_clean_bandwidth_trend.dat' using (\$1/60):2 with linespoints linestyle 3 title '1M'
        
        # 下半部分：写入带宽趋势对比
        set title '写入带宽趋势对比' font 'Arial,16'
        set xlabel '时间 (分钟)' font 'Arial,14'
        set ylabel '写入带宽 (MB/s)' font 'Arial,14'
        
        plot '$RESULT_DIR/mixed_rw_4k_clean_bandwidth_trend.dat' using (\$1/60):3 with linespoints linestyle 1 title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_clean_bandwidth_trend.dat' using (\$1/60):3 with linespoints linestyle 2 title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_clean_bandwidth_trend.dat' using (\$1/60):3 with linespoints linestyle 3 title '1M'
        
        unset multiplot
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成综合简洁趋势对比图: $chart_file"
    else
        echo "✗ 生成综合简洁趋势对比图失败"
    fi
}

# 主程序
echo "开始生成简洁趋势线图表..."
echo "数据采样间隔: 30秒 (减少数据密度)"
echo

# 为每个块大小生成简洁趋势图
for bs in "${BLOCK_SIZES[@]}"; do
    echo "处理块大小: $bs"
    generate_clean_bandwidth_trend "$bs"
    generate_clean_iops_trend "$bs"
    echo
done

# 生成综合对比图
generate_clean_combined_trend

echo
echo "=== 简洁趋势图表生成完成 ==="
echo
echo "生成的简洁趋势图表文件："
find "$RESULT_DIR" -name "*clean*trend*.png" | sort | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  📈 $(basename "$file") ($size)"
done

echo
echo "🎯 简洁图表特点："
echo "  - 数据采样间隔: 30秒 (大大减少数据点密度)"
echo "  - 清晰的趋势线: 线条+数据点标记"
echo "  - 时间轴: 以分钟为单位显示"
echo "  - 简洁布局: 类似您期望的图表样式"
