#!/bin/bash

# FIO图表改进效果演示脚本

RESULT_DIR="test_results"

echo "=== FIO 图表改进效果演示 ==="
echo

if [ ! -d "$RESULT_DIR" ]; then
    echo "错误: 测试结果目录 $RESULT_DIR 不存在"
    echo "请先运行 FIO 测试或复制现有测试结果"
    exit 1
fi

echo "测试结果目录: $RESULT_DIR"
echo "包含的测试数据:"
ls -la "$RESULT_DIR"/*.log | head -5
echo "... (共 $(ls "$RESULT_DIR"/*.log | wc -l) 个日志文件)"
echo

# 生成原始风格图表（用于对比）
echo "1. 生成原始风格图表（用于对比）..."
for bs in "4k" "128k" "1M"; do
    LOG_PREFIX="$RESULT_DIR/mixed_rw_${bs}"
    CHART_FILE="${LOG_PREFIX}_original_chart.png"
    SOURCE_LOG_FILE="${LOG_PREFIX}_bw.1.log"
    
    if [ -f "${SOURCE_LOG_FILE}" ]; then
        gnuplot -e "
            set terminal pngcairo size 1200,600 font 'Verdana,10';
            set output '${CHART_FILE}';
            set title 'NVMe Performance (80% Read / 20% Write, Block Size: ${bs}) - Original Style';
            set xlabel 'Time (seconds)';
            set ylabel 'Bandwidth (MB/s)';
            set grid;
            set key top right;
            set format x '%g';
            
            plot '${SOURCE_LOG_FILE}' using (\$1/1000):(\$2/1024) with lines title 'Read BW (MB/s)', \\
                 '${SOURCE_LOG_FILE}' using (\$1/1000):(\$3/1024) with lines title 'Write BW (MB/s)';
        "
        echo "  ✓ 生成原始图表: ${CHART_FILE}"
    fi
done

echo

# 生成改进风格图表
echo "2. 生成改进风格图表..."
./generate_improved_gnuplot_charts.sh "$RESULT_DIR"

echo

# 显示对比结果
echo "3. 图表对比结果:"
echo
echo "原始图表 (单job数据, 简单样式):"
find "$RESULT_DIR" -name "*original_chart.png" | sort | while read file; do
    echo "  📊 $(basename "$file")"
done

echo
echo "改进图表 (多job合并, 现代样式):"
find "$RESULT_DIR" -name "*improved*chart.png" | sort | while read file; do
    echo "  📈 $(basename "$file")"
done

echo
echo "汇总对比图表:"
find "$RESULT_DIR" -name "*summary*chart.png" | sort | while read file; do
    echo "  📋 $(basename "$file")"
done

echo
echo "=== 改进效果总结 ==="
echo
echo "📊 原始图表特点:"
echo "   - 只使用单个job数据 (如 mixed_rw_4k_bw.1.log)"
echo "   - 数据密集，噪声较多"
echo "   - 简单的线条样式"
echo "   - 基础的网格和标签"
echo
echo "📈 改进图表特点:"
echo "   - 合并所有job数据 (如 56个job的数据)"
echo "   - 数据平滑，趋势清晰"
echo "   - 现代化样式和配色"
echo "   - 专业的图表布局"
echo "   - 同时提供带宽和IOPS视角"
echo
echo "📋 新增汇总图表:"
echo "   - 四象限对比布局"
echo "   - 不同块大小性能对比"
echo "   - 读写性能分别展示"
echo "   - 便于快速性能评估"
echo
echo "🎯 使用建议:"
echo "   - 日常测试: 使用改进图表，更清晰准确"
echo "   - 性能报告: 使用汇总图表，便于对比分析"
echo "   - 详细分析: 结合带宽和IOPS图表"
echo
echo "📁 查看图表文件:"
echo "   ls $RESULT_DIR/*.png"
echo
