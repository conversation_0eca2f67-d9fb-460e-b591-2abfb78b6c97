#!/bin/bash

# FIO 简洁趋势线图表生成器 (简化版)
# 生成类似您期望的简洁清晰的趋势线图表

RESULT_DIR="$1"
BLOCK_SIZES=("4k" "128k" "1M")

if [ -z "$RESULT_DIR" ]; then
    echo "用法: $0 <测试结果目录>"
    exit 1
fi

echo "=== FIO 简洁趋势线图表生成器 (简化版) ==="
echo "处理测试结果目录: $RESULT_DIR"
echo

# 函数：生成简洁的带宽趋势图
generate_simple_clean_bandwidth_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_simple_clean_bandwidth_trend.png"
    local data_file="${log_prefix}_simple_clean_bandwidth_trend.dat"
    
    echo "生成 ${block_size} 简洁带宽趋势图..."
    
    # 使用第一个job的数据，但进行采样处理
    local source_file="${log_prefix}_bw.1.log"
    
    if [ ! -f "$source_file" ]; then
        echo "警告: 找不到源文件 $source_file"
        return 1
    fi
    
    # 创建数据文件头部
    echo "# Time(min) Read_BW(MB/s) Write_BW(MB/s)" > "$data_file"
    
    # 处理数据：每60秒取一个平均值，减少数据密度
    awk -F',' '
    BEGIN {
        window_size = 60000  # 60秒窗口，单位毫秒
    }
    {
        # 去除空格
        gsub(/[ \t]/, "", $1)
        gsub(/[ \t]/, "", $2)
        gsub(/[ \t]/, "", $3)
        
        if (NF >= 4 && $1 > 0) {
            timestamp = $1
            bandwidth = $2  # KB/s
            direction = $3  # 0=read, 1=write
            
            # 计算时间窗口（分钟）
            window = int(timestamp / window_size)
            
            if (direction == 0) {
                read_sum[window] += bandwidth
                read_count[window]++
            } else {
                write_sum[window] += bandwidth
                write_count[window]++
            }
        }
    }
    END {
        # 输出平均值
        for (w in read_sum) {
            time_min = w
            read_avg_mb = (read_count[w] > 0) ? read_sum[w] / read_count[w] / 1024 : 0
            write_avg_mb = (write_count[w] > 0) ? write_sum[w] / write_count[w] / 1024 : 0
            print time_min, read_avg_mb, write_avg_mb
        }
    }' "$source_file" | sort -n >> "$data_file"
    
    # 检查数据文件
    local data_points=$(( $(wc -l < "$data_file") - 1 ))
    if [ $data_points -le 0 ]; then
        echo "警告: 没有找到有效的带宽数据"
        return 1
    fi
    
    # 生成简洁的趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1200,700 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe 带宽性能趋势 (块大小: ${block_size})' font 'Arial,16'
        set xlabel '时间 (分钟)' font 'Arial,14'
        set ylabel '带宽 (MB/s)' font 'Arial,14'
        
        # 简洁的网格设置
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#E8E8E8'
        
        # 图例设置
        set key top right
        set key box linewidth 1
        set key font 'Arial,12'
        
        # 坐标轴设置
        set xtics font 'Arial,11'
        set ytics font 'Arial,11'
        set border linewidth 1.5
        
        # 设置简洁的线条样式 - 类似您期望的样式
        set style line 1 linecolor rgb '#1f77b4' linewidth 3 pointtype 7 pointsize 1.5
        set style line 2 linecolor rgb '#ff7f0e' linewidth 3 pointtype 5 pointsize 1.5
        
        # 绘制简洁的趋势线（线条+数据点）
        plot '$data_file' using 1:2 with linespoints linestyle 1 title '读取带宽', \\
             '$data_file' using 1:3 with linespoints linestyle 2 title '写入带宽'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成简洁带宽趋势图: $chart_file"
        echo "  数据点数量: $data_points 个点 (每分钟1个点)"
    else
        echo "✗ 生成简洁带宽趋势图失败"
        return 1
    fi
}

# 函数：生成简洁的IOPS趋势图
generate_simple_clean_iops_trend() {
    local block_size="$1"
    local log_prefix="$RESULT_DIR/mixed_rw_${block_size}"
    local chart_file="${log_prefix}_simple_clean_iops_trend.png"
    local data_file="${log_prefix}_simple_clean_iops_trend.dat"
    
    echo "生成 ${block_size} 简洁IOPS趋势图..."
    
    # 使用第一个job的数据
    local source_file="${log_prefix}_iops.1.log"
    
    if [ ! -f "$source_file" ]; then
        echo "警告: 找不到源文件 $source_file"
        return 1
    fi
    
    # 创建数据文件头部
    echo "# Time(min) Read_IOPS Write_IOPS" > "$data_file"
    
    # 处理数据：每60秒取一个平均值
    awk -F',' '
    BEGIN {
        window_size = 60000  # 60秒窗口，单位毫秒
    }
    {
        # 去除空格
        gsub(/[ \t]/, "", $1)
        gsub(/[ \t]/, "", $2)
        gsub(/[ \t]/, "", $3)
        
        if (NF >= 4 && $1 > 0) {
            timestamp = $1
            iops = $2
            direction = $3  # 0=read, 1=write
            
            # 计算时间窗口（分钟）
            window = int(timestamp / window_size)
            
            if (direction == 0) {
                read_sum[window] += iops
                read_count[window]++
            } else {
                write_sum[window] += iops
                write_count[window]++
            }
        }
    }
    END {
        # 输出平均值
        for (w in read_sum) {
            time_min = w
            read_avg = (read_count[w] > 0) ? read_sum[w] / read_count[w] : 0
            write_avg = (write_count[w] > 0) ? write_sum[w] / write_count[w] : 0
            print time_min, read_avg, write_avg
        }
    }' "$source_file" | sort -n >> "$data_file"
    
    # 检查数据文件
    local data_points=$(( $(wc -l < "$data_file") - 1 ))
    if [ $data_points -le 0 ]; then
        echo "警告: 没有找到有效的IOPS数据"
        return 1
    fi
    
    # 生成简洁的趋势线图表
    gnuplot << EOF
        set terminal pngcairo size 1200,700 font 'Arial,14'
        set output '$chart_file'
        
        # 图表标题和标签
        set title 'NVMe IOPS性能趋势 (块大小: ${block_size})' font 'Arial,16'
        set xlabel '时间 (分钟)' font 'Arial,14'
        set ylabel 'IOPS' font 'Arial,14'
        
        # 简洁的网格设置
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#E8E8E8'
        
        # 图例设置
        set key top right
        set key box linewidth 1
        set key font 'Arial,12'
        
        # 坐标轴设置
        set xtics font 'Arial,11'
        set ytics font 'Arial,11'
        set border linewidth 1.5
        
        # 设置简洁的线条样式
        set style line 1 linecolor rgb '#2ca02c' linewidth 3 pointtype 7 pointsize 1.5
        set style line 2 linecolor rgb '#d62728' linewidth 3 pointtype 5 pointsize 1.5
        
        # 绘制简洁的趋势线（线条+数据点）
        plot '$data_file' using 1:2 with linespoints linestyle 1 title '读取 IOPS', \\
             '$data_file' using 1:3 with linespoints linestyle 2 title '写入 IOPS'
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成简洁IOPS趋势图: $chart_file"
        echo "  数据点数量: $data_points 个点 (每分钟1个点)"
    else
        echo "✗ 生成简洁IOPS趋势图失败"
        return 1
    fi
}

# 函数：生成综合简洁对比图
generate_simple_combined_trend() {
    local chart_file="$RESULT_DIR/simple_clean_performance_trends_combined.png"
    
    echo "生成综合简洁性能趋势对比图..."
    
    # 检查数据文件是否存在
    local has_data=false
    for bs in "${BLOCK_SIZES[@]}"; do
        local data_file="$RESULT_DIR/mixed_rw_${bs}_simple_clean_bandwidth_trend.dat"
        if [ -f "$data_file" ] && [ $(wc -l < "$data_file") -gt 1 ]; then
            has_data=true
            break
        fi
    done
    
    if [ "$has_data" = false ]; then
        echo "警告: 没有找到有效的趋势数据文件"
        return 1
    fi
    
    # 生成综合对比图表
    gnuplot << EOF
        set terminal pngcairo size 1400,900 font 'Arial,12'
        set output '$chart_file'
        
        # 设置多图布局
        set multiplot layout 2,1 title 'NVMe 性能趋势对比 (简洁版)' font 'Arial,16'
        
        # 上半部分：读取带宽趋势对比
        set title '读取带宽趋势对比' font 'Arial,14'
        set xlabel '时间 (分钟)' font 'Arial,12'
        set ylabel '读取带宽 (MB/s)' font 'Arial,12'
        set grid xtics ytics
        set grid linewidth 1 linetype 0 linecolor rgb '#F0F0F0'
        set key top right font 'Arial,11'
        
        set style line 1 linecolor rgb '#1f77b4' linewidth 2.5 pointtype 7 pointsize 1.2
        set style line 2 linecolor rgb '#ff7f0e' linewidth 2.5 pointtype 5 pointsize 1.2
        set style line 3 linecolor rgb '#2ca02c' linewidth 2.5 pointtype 9 pointsize 1.2
        
        plot '$RESULT_DIR/mixed_rw_4k_simple_clean_bandwidth_trend.dat' using 1:2 with linespoints linestyle 1 title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_simple_clean_bandwidth_trend.dat' using 1:2 with linespoints linestyle 2 title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_simple_clean_bandwidth_trend.dat' using 1:2 with linespoints linestyle 3 title '1M'
        
        # 下半部分：写入带宽趋势对比
        set title '写入带宽趋势对比' font 'Arial,14'
        set xlabel '时间 (分钟)' font 'Arial,12'
        set ylabel '写入带宽 (MB/s)' font 'Arial,12'
        
        plot '$RESULT_DIR/mixed_rw_4k_simple_clean_bandwidth_trend.dat' using 1:3 with linespoints linestyle 1 title '4K', \\
             '$RESULT_DIR/mixed_rw_128k_simple_clean_bandwidth_trend.dat' using 1:3 with linespoints linestyle 2 title '128K', \\
             '$RESULT_DIR/mixed_rw_1M_simple_clean_bandwidth_trend.dat' using 1:3 with linespoints linestyle 3 title '1M'
        
        unset multiplot
EOF
    
    if [ $? -eq 0 ] && [ -f "$chart_file" ] && [ -s "$chart_file" ]; then
        echo "✓ 生成综合简洁趋势对比图: $chart_file"
    else
        echo "✗ 生成综合简洁趋势对比图失败"
    fi
}

# 主程序
echo "开始生成简洁趋势线图表..."
echo "数据采样: 每分钟1个数据点 (大大减少密度)"
echo

# 为每个块大小生成简洁趋势图
for bs in "${BLOCK_SIZES[@]}"; do
    echo "处理块大小: $bs"
    generate_simple_clean_bandwidth_trend "$bs"
    generate_simple_clean_iops_trend "$bs"
    echo
done

# 生成综合对比图
generate_simple_combined_trend

echo
echo "=== 简洁趋势图表生成完成 ==="
echo
echo "生成的简洁趋势图表文件："
find "$RESULT_DIR" -name "*simple_clean*trend*.png" | sort | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  📈 $(basename "$file") ($size)"
done

echo
echo "🎯 简洁图表特点："
echo "  - 数据采样: 每分钟1个数据点 (60倍减少密度)"
echo "  - 清晰的趋势线: 线条+明显的数据点标记"
echo "  - 时间轴: 以分钟为单位"
echo "  - 简洁布局: 类似您期望的图表样式"
echo "  - 易于观察: 清晰显示性能变化趋势"
