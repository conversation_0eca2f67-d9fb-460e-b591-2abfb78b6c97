set terminal png size 1200,800 font "Arial,14"
set output "/check/performance_comparison_chart.png"

set title "FIO Performance Comparison (Test2 vs Test1 Baseline)" font "Arial,18"
set xlabel "Test Types" font "Arial,14"
set ylabel "Performance Ratio" font "Arial,14"

set style data histograms
set style histogram clustered gap 1
set style fill solid border -1
set boxwidth 0.8

set xtics rotate by -45
set grid ytics
set yrange [0.95:1.1]

# Add baseline reference line at y=1.0
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 3 dt 2

set key outside right top

plot "/check/performance_data.dat" using 2:xtic(1) title "Read IOPS" lc rgb "blue", \
     "" using 3 title "Write IOPS" lc rgb "green", \
     "" using 4 title "Read Bandwidth" lc rgb "cyan", \
     "" using 5 title "Write Bandwidth" lc rgb "orange"
