<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO 硬盘性能对比报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        .summary {
            background-color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #bdc3c7;
        }
        .test-result.pass {
            background-color: #d5f4e6;
            border-left-color: #27ae60;
        }
        .test-result.fail {
            background-color: #fadbd8;
            border-left-color: #e74c3c;
        }
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .performance-table th,
        .performance-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .performance-table th {
            background-color: #3498db;
            color: white;
        }
        .performance-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .status-pass {
            color: #27ae60;
            font-weight: bold;
        }
        .status-fail {
            color: #e74c3c;
            font-weight: bold;
        }
        .ratio-bar {
            display: inline-block;
            height: 20px;
            background-color: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            width: 200px;
            margin: 0 10px;
        }
        .ratio-fill {
            height: 100%;
            transition: width 0.3s ease;
        }
        .ratio-pass {
            background-color: #27ae60;
        }
        .ratio-fail {
            background-color: #e74c3c;
        }
        .recommendations {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .metric {
            display: inline-block;
            margin: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            min-width: 150px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .metric-label {
            font-size: 14px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 FIO 硬盘性能对比报告</h1>
        
        <div class="summary">
            <h2>📊 测试概览</h2>
            <p><strong>基准测试 (测试1):</strong> /check/fio-web-controller/hp/</p>
            <p><strong>对比测试 (测试2):</strong> /check/fio_results_20250716_121731/</p>
            <p><strong>测试时间:</strong> 2025年7月16日</p>
            <p><strong>测试标准:</strong> 性能比率 ≥ 1.0 为 PASS，< 1.0 为 FAIL</p>
        </div>

        <h2>📈 整体性能汇总</h2>
        <div style="text-align: center;">
            <div class="metric">
                <div class="metric-value status-pass">2/3</div>
                <div class="metric-label">测试项目通过</div>
            </div>
            <div class="metric">
                <div class="metric-value">66.7%</div>
                <div class="metric-label">通过率</div>
            </div>
            <div class="metric">
                <div class="metric-value">1.040</div>
                <div class="metric-label">平均性能比率</div>
            </div>
        </div>

        <h2>📋 详细测试结果</h2>
        
        <div class="test-result fail">
            <h3>🔴 4K 混合读写测试 - FAIL</h3>
            <p><strong>读性能:</strong> 165,000 IOPS → 163,000 IOPS (比率: 0.988) 
               <span class="ratio-bar"><span class="ratio-fill ratio-fail" style="width: 98.8%"></span></span></p>
            <p><strong>写性能:</strong> 41,400 IOPS → 40,800 IOPS (比率: 0.986)
               <span class="ratio-bar"><span class="ratio-fill ratio-fail" style="width: 98.6%"></span></span></p>
            <p><strong>分析:</strong> 4K小文件性能略有下降，可能是由于硬盘缓存策略或控制器差异导致。</p>
        </div>

        <div class="test-result pass">
            <h3>🟢 128K 混合读写测试 - PASS</h3>
            <p><strong>读性能:</strong> 5,858 IOPS → 6,134 IOPS (比率: 1.047)
               <span class="ratio-bar"><span class="ratio-fill ratio-pass" style="width: 100%"></span></span></p>
            <p><strong>写性能:</strong> 1,464 IOPS → 1,534 IOPS (比率: 1.048)
               <span class="ratio-bar"><span class="ratio-fill ratio-pass" style="width: 100%"></span></span></p>
            <p><strong>分析:</strong> 中等块大小性能表现优秀，提升约4.8%，说明硬盘在中等负载下表现良好。</p>
        </div>

        <div class="test-result pass">
            <h3>🟢 1M 混合读写测试 - PASS</h3>
            <p><strong>读性能:</strong> 692 IOPS → 750 IOPS (比率: 1.084)
               <span class="ratio-bar"><span class="ratio-fill ratio-pass" style="width: 100%"></span></span></p>
            <p><strong>写性能:</strong> 173 IOPS → 187 IOPS (比率: 1.081)
               <span class="ratio-bar"><span class="ratio-fill ratio-pass" style="width: 100%"></span></span></p>
            <p><strong>分析:</strong> 大文件性能表现最佳，提升约8%，表明硬盘在大块数据传输方面有优势。</p>
        </div>

        <h2>📊 性能对比表</h2>
        <table class="performance-table">
            <thead>
                <tr>
                    <th>测试项目</th>
                    <th>读 IOPS 比率</th>
                    <th>读状态</th>
                    <th>写 IOPS 比率</th>
                    <th>写状态</th>
                    <th>整体状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>4K 混合读写</td>
                    <td>0.988</td>
                    <td><span class="status-fail">FAIL</span></td>
                    <td>0.986</td>
                    <td><span class="status-fail">FAIL</span></td>
                    <td><span class="status-fail">FAIL</span></td>
                </tr>
                <tr>
                    <td>128K 混合读写</td>
                    <td>1.047</td>
                    <td><span class="status-pass">PASS</span></td>
                    <td>1.048</td>
                    <td><span class="status-pass">PASS</span></td>
                    <td><span class="status-pass">PASS</span></td>
                </tr>
                <tr>
                    <td>1M 混合读写</td>
                    <td>1.084</td>
                    <td><span class="status-pass">PASS</span></td>
                    <td>1.081</td>
                    <td><span class="status-pass">PASS</span></td>
                    <td><span class="status-pass">PASS</span></td>
                </tr>
            </tbody>
        </table>

        <div class="recommendations">
            <h2>💡 分析建议</h2>
            <h3>✅ 优势方面:</h3>
            <ul>
                <li><strong>大文件处理:</strong> 在128K和1M测试中表现优秀，适合大文件传输和备份场景</li>
                <li><strong>整体稳定性:</strong> 大部分测试项目通过，硬盘整体性能稳定</li>
                <li><strong>写入性能:</strong> 在中大型文件写入方面有明显提升</li>
            </ul>
            
            <h3>⚠️ 需要关注:</h3>
            <ul>
                <li><strong>小文件性能:</strong> 4K测试略有下降，可能影响系统响应速度和数据库性能</li>
                <li><strong>随机访问:</strong> 小块随机读写性能需要进一步优化</li>
            </ul>
            
            <h3>🔧 优化建议:</h3>
            <ul>
                <li>检查硬盘缓存设置和队列深度配置</li>
                <li>考虑针对小文件场景进行系统调优</li>
                <li>监控硬盘温度和健康状态</li>
                <li>定期进行性能基准测试以跟踪变化趋势</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>报告生成时间: <script>document.write(new Date().toLocaleString('zh-CN'));</script></p>
            <p>测试工具: FIO (Flexible I/O Tester)</p>
        </div>
    </div>
</body>
</html>
