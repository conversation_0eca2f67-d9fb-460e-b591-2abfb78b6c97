#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建性能对比图表 - 使用多种方法生成可视化图表
"""

import os
import re
import subprocess
from pathlib import Path

def parse_summary_file(file_path):
    """解析FIO汇总文件，提取关键性能指标"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # 提取读写性能数据
        read_match = re.search(r'read: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
        write_match = re.search(r'write: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s', content)
        
        def parse_iops(iops_str):
            if 'k' in iops_str:
                return float(iops_str.replace('k', '')) * 1000
            return float(iops_str)
        
        result = {}
        if read_match:
            result['read_iops'] = parse_iops(read_match.group(1))
            result['read_bw'] = float(read_match.group(2))
        
        if write_match:
            result['write_iops'] = parse_iops(write_match.group(1))
            result['write_bw'] = float(write_match.group(2))
            
        return result
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return {}

def collect_data():
    """收集性能数据"""
    baseline_path = Path("/check/fio-web-controller/hp")
    test_path = Path("/check/fio_results_20250716_121731")
    
    test_types = ['mixed_rw_4k', 'mixed_rw_128k', 'mixed_rw_1M']
    results = {}
    
    for test_type in test_types:
        baseline_file = baseline_path / f"{test_type}_summary.txt"
        test_file = test_path / f"{test_type}_summary.txt"
        
        if baseline_file.exists() and test_file.exists():
            baseline_data = parse_summary_file(baseline_file)
            test_data = parse_summary_file(test_file)
            
            results[test_type] = {
                'baseline': baseline_data,
                'test': test_data
            }
    
    return results

def create_gnuplot_chart(results):
    """使用gnuplot创建图表"""
    # 准备数据
    comparison_data = []
    for test_type, data in results.items():
        baseline = data['baseline']
        test = data['test']
        
        if 'read_iops' in baseline and 'read_iops' in test:
            read_ratio = test['read_iops'] / baseline['read_iops']
            write_ratio = test['write_iops'] / baseline['write_iops']
            read_bw_ratio = test['read_bw'] / baseline['read_bw']
            write_bw_ratio = test['write_bw'] / baseline['write_bw']
            
            comparison_data.append({
                'name': test_type.replace('mixed_rw_', ''),
                'read_iops_ratio': read_ratio,
                'write_iops_ratio': write_ratio,
                'read_bw_ratio': read_bw_ratio,
                'write_bw_ratio': write_bw_ratio
            })
    
    # 创建数据文件
    data_file = '/check/performance_data.dat'
    with open(data_file, 'w') as f:
        f.write("# Test_Type Read_IOPS_Ratio Write_IOPS_Ratio Read_BW_Ratio Write_BW_Ratio\n")
        for item in comparison_data:
            f.write(f"{item['name']} {item['read_iops_ratio']:.3f} {item['write_iops_ratio']:.3f} "
                   f"{item['read_bw_ratio']:.3f} {item['write_bw_ratio']:.3f}\n")
    
    # 创建gnuplot脚本
    gnuplot_script = '''
set terminal png size 1400,900 font "Arial,14"
set output "/check/performance_comparison_chart.png"

set multiplot layout 2,2 title "FIO Performance Comparison (Test2 vs Test1)" font "Arial,18"

# IOPS Comparison
set title "IOPS Performance Ratio" font "Arial,16"
set xlabel "Test Types"
set ylabel "Performance Ratio"
set style data histograms
set style histogram clustered gap 1
set style fill solid border -1
set boxwidth 0.8
set xtics rotate by -45
set grid ytics
set yrange [0.9:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
plot "/check/performance_data.dat" using 2:xtic(1) title "Read IOPS" lc rgb "blue", \\
     "" using 3 title "Write IOPS" lc rgb "green"

# Bandwidth Comparison  
set title "Bandwidth Performance Ratio" font "Arial,16"
set xlabel "Test Types"
set ylabel "Performance Ratio"
set yrange [0.9:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
plot "/check/performance_data.dat" using 4:xtic(1) title "Read BW" lc rgb "cyan", \\
     "" using 5 title "Write BW" lc rgb "orange"

# Performance Summary
set title "Overall Performance Status" font "Arial,16"
set xlabel "Test Types"
set ylabel "Pass/Fail Status"
set yrange [0:2]
set ytics ("FAIL" 0, "PASS" 1)
set style fill solid
plot "/check/performance_data.dat" using ($2>=1.0 && $3>=1.0 ? 1 : 0):xtic(1) with boxes title "Overall Status" lc variable

# Detailed Numbers
set title "Performance Ratios (Numerical)" font "Arial,16"
set xlabel "Metrics"
set ylabel "Ratio Values"
set xtics rotate by -45
set yrange [0.95:1.1]
set arrow from graph 0,first 1.0 to graph 1,first 1.0 nohead lc rgb "red" lw 2 dt 2
set style data linespoints
plot "/check/performance_data.dat" using 2 title "4K Read" with linespoints pt 7 ps 1.5, \\
     "" using 3 title "4K Write" with linespoints pt 7 ps 1.5, \\
     "" using 4 title "128K Read" with linespoints pt 7 ps 1.5, \\
     "" using 5 title "128K Write" with linespoints pt 7 ps 1.5

unset multiplot
'''
    
    script_file = '/check/plot_script.gp'
    with open(script_file, 'w') as f:
        f.write(gnuplot_script)
    
    # 尝试执行gnuplot
    try:
        result = subprocess.run(['gnuplot', script_file], 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            print(f"✅ Gnuplot图表已生成: /check/performance_comparison_chart.png")
            return True
        else:
            print(f"❌ Gnuplot错误: {result.stderr.decode()}")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
        print("⚠️  Gnuplot不可用，将创建HTML图表...")
        return False

def create_html_chart(results):
    """创建交互式HTML图表"""
    # 准备数据
    comparison_data = []
    for test_type, data in results.items():
        baseline = data['baseline']
        test = data['test']
        
        if 'read_iops' in baseline and 'read_iops' in test:
            read_ratio = test['read_iops'] / baseline['read_iops']
            write_ratio = test['write_iops'] / baseline['write_iops']
            
            comparison_data.append({
                'name': test_type.replace('mixed_rw_', ''),
                'read_ratio': read_ratio,
                'write_ratio': write_ratio,
                'read_baseline': baseline['read_iops'],
                'read_test': test['read_iops'],
                'write_baseline': baseline['write_iops'],
                'write_test': test['write_iops']
            })
    
    html_content = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIO Performance Comparison Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f5f5f5;
        }}
        .container {{ 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{ 
            text-align: center; 
            color: #2c3e50; 
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        .chart-container {{ 
            position: relative; 
            height: 500px; 
            margin: 30px 0; 
        }}
        .summary {{ 
            display: flex; 
            justify-content: space-around; 
            margin: 20px 0; 
            padding: 20px; 
            background-color: #ecf0f1; 
            border-radius: 8px;
        }}
        .metric {{ 
            text-align: center; 
            padding: 15px;
        }}
        .metric-value {{ 
            font-size: 24px; 
            font-weight: bold; 
            color: #2c3e50;
        }}
        .metric-label {{ 
            font-size: 14px; 
            color: #7f8c8d; 
            margin-top: 5px;
        }}
        .status-pass {{ color: #27ae60; }}
        .status-fail {{ color: #e74c3c; }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        .data-table th, .data-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }}
        .data-table th {{
            background-color: #3498db;
            color: white;
        }}
        .data-table tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 FIO 硬盘性能对比图表</h1>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value">2/3</div>
                <div class="metric-label">测试通过</div>
            </div>
            <div class="metric">
                <div class="metric-value">66.7%</div>
                <div class="metric-label">通过率</div>
            </div>
            <div class="metric">
                <div class="metric-value">1.040</div>
                <div class="metric-label">平均性能比率</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
        
        <h2>📋 详细数据表</h2>
        <table class="data-table">
            <thead>
                <tr>
                    <th>测试类型</th>
                    <th>读IOPS (基准)</th>
                    <th>读IOPS (测试)</th>
                    <th>读比率</th>
                    <th>写IOPS (基准)</th>
                    <th>写IOPS (测试)</th>
                    <th>写比率</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>'''
    
    for item in comparison_data:
        status_class = "status-pass" if item['read_ratio'] >= 1.0 and item['write_ratio'] >= 1.0 else "status-fail"
        status_text = "PASS" if item['read_ratio'] >= 1.0 and item['write_ratio'] >= 1.0 else "FAIL"
        
        html_content += f'''
                <tr>
                    <td>{item['name']}</td>
                    <td>{item['read_baseline']:,.0f}</td>
                    <td>{item['read_test']:,.0f}</td>
                    <td>{item['read_ratio']:.3f}</td>
                    <td>{item['write_baseline']:,.0f}</td>
                    <td>{item['write_test']:,.0f}</td>
                    <td>{item['write_ratio']:.3f}</td>
                    <td><span class="{status_class}">{status_text}</span></td>
                </tr>'''
    
    # 准备Chart.js数据
    labels = [item['name'] for item in comparison_data]
    read_ratios = [item['read_ratio'] for item in comparison_data]
    write_ratios = [item['write_ratio'] for item in comparison_data]
    
    html_content += f'''
            </tbody>
        </table>
        
        <script>
            const ctx = document.getElementById('performanceChart').getContext('2d');
            const chart = new Chart(ctx, {{
                type: 'bar',
                data: {{
                    labels: {labels},
                    datasets: [
                        {{
                            label: '读 IOPS 比率',
                            data: {read_ratios},
                            backgroundColor: 'rgba(54, 162, 235, 0.8)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2
                        }},
                        {{
                            label: '写 IOPS 比率',
                            data: {write_ratios},
                            backgroundColor: 'rgba(75, 192, 192, 0.8)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2
                        }}
                    ]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '性能比率对比 (测试2 / 测试1基准)',
                            font: {{
                                size: 16
                            }}
                        }},
                        legend: {{
                            position: 'top',
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            max: 1.2,
                            ticks: {{
                                callback: function(value) {{
                                    return value.toFixed(2);
                                }}
                            }},
                            grid: {{
                                color: function(context) {{
                                    if (Math.abs(context.tick.value - 1.0) < 0.001) {{
                                        return 'red';
                                    }}
                                    return 'rgba(0, 0, 0, 0.1)';
                                }},
                                lineWidth: function(context) {{
                                    if (Math.abs(context.tick.value - 1.0) < 0.001) {{
                                        return 3;
                                    }}
                                    return 1;
                                }}
                            }}
                        }}
                    }}
                }}
            }});
        </script>
        
        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>📈 红线表示基准线 (比率 = 1.0)</p>
            <p>🟢 绿色区域表示性能提升，🔴 红色区域表示性能下降</p>
            <p>生成时间: <script>document.write(new Date().toLocaleString('zh-CN'));</script></p>
        </div>
    </div>
</body>
</html>'''
    
    with open('/check/performance_chart.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ HTML图表已生成: /check/performance_chart.html")
    return True

def main():
    print("🔄 正在收集性能数据...")
    results = collect_data()
    
    if not results:
        print("❌ 未找到测试数据文件")
        return
    
    print("📊 正在生成图表...")
    
    # 尝试使用gnuplot
    if not create_gnuplot_chart(results):
        # 如果gnuplot失败，使用HTML图表
        create_html_chart(results)
    
    print("✅ 图表生成完成！")
    print("\n📁 生成的文件:")
    print("   - performance_comparison_chart.png (如果gnuplot可用)")
    print("   - performance_chart.html (交互式HTML图表)")
    print("   - performance_data.dat (原始数据)")

if __name__ == "__main__":
    main()
