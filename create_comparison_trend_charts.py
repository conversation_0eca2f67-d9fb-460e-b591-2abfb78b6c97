#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建两个硬盘测试的时间序列对比图表
"""

import os
import subprocess
from pathlib import Path

def create_comparison_trend_charts():
    """创建对比的时间序列图表"""
    
    baseline_path = Path("/check/fio-web-controller/hp")
    test_path = Path("/check/fio_results_20250716_121731")
    
    test_types = ['mixed_rw_4k', 'mixed_rw_128k', 'mixed_rw_1M']
    
    for test_type in test_types:
        # 检查数据文件是否存在
        baseline_iops_file = baseline_path / f"{test_type}_simple_clean_iops_trend.dat"
        test_iops_file = test_path / f"{test_type}_simple_clean_iops_trend.dat"
        
        if not baseline_iops_file.exists() or not test_iops_file.exists():
            print(f"⚠️  跳过 {test_type}: 数据文件不存在")
            continue
        
        # 创建合并的数据文件
        combined_data_file = f"/check/{test_type}_comparison_trend.dat"
        create_combined_data_file(baseline_iops_file, test_iops_file, combined_data_file, test_type)
        
        # 创建gnuplot脚本
        gnuplot_script = create_gnuplot_script(test_type, combined_data_file)
        script_file = f"/check/{test_type}_comparison_plot.gp"
        
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(gnuplot_script)
        
        # 执行gnuplot
        try:
            result = subprocess.run(['gnuplot', script_file], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
            if result.returncode == 0:
                print(f"✅ {test_type} 对比图表已生成: /check/{test_type}_comparison_chart.png")
            else:
                print(f"❌ {test_type} Gnuplot错误: {result.stderr.decode()}")
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError) as e:
            print(f"⚠️  {test_type} Gnuplot执行失败: {e}")

def create_combined_data_file(baseline_file, test_file, output_file, test_type):
    """合并两个测试的数据文件"""
    
    # 读取基准数据
    baseline_data = {}
    with open(baseline_file, 'r') as f:
        for line in f:
            if line.startswith('#') or not line.strip():
                continue
            parts = line.strip().split()
            if len(parts) >= 3:
                time_min = float(parts[0])
                read_iops = float(parts[1])
                write_iops = float(parts[2])
                baseline_data[time_min] = (read_iops, write_iops)
    
    # 读取测试数据
    test_data = {}
    with open(test_file, 'r') as f:
        for line in f:
            if line.startswith('#') or not line.strip():
                continue
            parts = line.strip().split()
            if len(parts) >= 3:
                time_min = float(parts[0])
                read_iops = float(parts[1])
                write_iops = float(parts[2])
                test_data[time_min] = (read_iops, write_iops)
    
    # 合并数据并写入输出文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Time(min) Baseline_Read_IOPS Baseline_Write_IOPS Test_Read_IOPS Test_Write_IOPS\n")
        
        # 获取所有时间点
        all_times = sorted(set(baseline_data.keys()) | set(test_data.keys()))
        
        for time_min in all_times:
            baseline_read = baseline_data.get(time_min, (0, 0))[0]
            baseline_write = baseline_data.get(time_min, (0, 0))[1]
            test_read = test_data.get(time_min, (0, 0))[0]
            test_write = test_data.get(time_min, (0, 0))[1]
            
            f.write(f"{time_min} {baseline_read} {baseline_write} {test_read} {test_write}\n")

def create_gnuplot_script(test_type, data_file):
    """创建gnuplot脚本"""
    
    # 根据测试类型设置标题和单位
    if test_type == 'mixed_rw_4k':
        title = "4K 混合读写性能对比"
        block_size = "4K"
    elif test_type == 'mixed_rw_128k':
        title = "128K 混合读写性能对比"
        block_size = "128K"
    elif test_type == 'mixed_rw_1M':
        title = "1M 混合读写性能对比"
        block_size = "1M"
    else:
        title = f"{test_type} 性能对比"
        block_size = test_type
    
    script = f'''
set terminal png size 1400,900 font "Arial,14"
set output "/check/{test_type}_comparison_chart.png"

set title "{title} - 时间序列对比" font "Arial,18"
set xlabel "时间 (分钟)" font "Arial,14"
set ylabel "IOPS" font "Arial,14"

set grid
set key outside right top

# 设置线条样式
set style line 1 lc rgb "blue" lw 2 pt 7 ps 0.8
set style line 2 lc rgb "red" lw 2 pt 7 ps 0.8
set style line 3 lc rgb "green" lw 2 pt 9 ps 0.8 dt 2
set style line 4 lc rgb "orange" lw 2 pt 9 ps 0.8 dt 2

# 设置图例
set key box opaque

plot "{data_file}" using 1:2 with linespoints ls 1 title "测试1(基准) - 读 IOPS", \\
     "" using 1:3 with linespoints ls 2 title "测试1(基准) - 写 IOPS", \\
     "" using 1:4 with linespoints ls 3 title "测试2 - 读 IOPS", \\
     "" using 1:5 with linespoints ls 4 title "测试2 - 写 IOPS"

# 添加文本说明
set label "块大小: {block_size}" at graph 0.02, graph 0.95 font "Arial,12"
set label "实线: 测试1(基准)" at graph 0.02, graph 0.90 font "Arial,10"
set label "虚线: 测试2" at graph 0.02, graph 0.87 font "Arial,10"
'''
    
    return script

def create_combined_overview_chart():
    """创建综合对比图表"""
    
    gnuplot_script = '''
set terminal png size 1600,1200 font "Arial,12"
set output "/check/all_tests_comparison_overview.png"

set multiplot layout 3,1 title "FIO 性能对比总览 - 测试1(基准) vs 测试2" font "Arial,20"

# 4K 测试
set title "4K 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_4k_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \\
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \\
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \\
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

# 128K 测试
set title "128K 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_128k_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \\
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \\
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \\
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

# 1M 测试
set title "1M 混合读写性能对比" font "Arial,16"
set xlabel "时间 (分钟)"
set ylabel "IOPS"
set grid
set key outside right top
plot "/check/mixed_rw_1M_comparison_trend.dat" using 1:2 with lines lw 2 lc rgb "blue" title "测试1-读", \\
     "" using 1:3 with lines lw 2 lc rgb "red" title "测试1-写", \\
     "" using 1:4 with lines lw 2 lc rgb "green" dt 2 title "测试2-读", \\
     "" using 1:5 with lines lw 2 lc rgb "orange" dt 2 title "测试2-写"

unset multiplot
'''
    
    with open('/check/overview_comparison_plot.gp', 'w', encoding='utf-8') as f:
        f.write(gnuplot_script)
    
    # 执行gnuplot
    try:
        result = subprocess.run(['gnuplot', '/check/overview_comparison_plot.gp'], 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            print(f"✅ 综合对比图表已生成: /check/all_tests_comparison_overview.png")
        else:
            print(f"❌ 综合图表 Gnuplot错误: {result.stderr.decode()}")
    except (subprocess.TimeoutExpired, FileNotFoundError, OSError) as e:
        print(f"⚠️  综合图表 Gnuplot执行失败: {e}")

def main():
    print("🔄 正在创建时间序列对比图表...")
    
    # 创建各个测试的对比图表
    create_comparison_trend_charts()
    
    # 创建综合对比图表
    print("\n🔄 正在创建综合对比图表...")
    create_combined_overview_chart()
    
    print("\n✅ 时间序列对比图表生成完成！")
    print("\n📁 生成的文件:")
    print("   - mixed_rw_4k_comparison_chart.png (4K测试对比)")
    print("   - mixed_rw_128k_comparison_chart.png (128K测试对比)")
    print("   - mixed_rw_1M_comparison_chart.png (1M测试对比)")
    print("   - all_tests_comparison_overview.png (综合对比图表)")
    print("\n📊 图表说明:")
    print("   - 实线: 测试1(基准硬盘)")
    print("   - 虚线: 测试2(对比硬盘)")
    print("   - 蓝色/绿色: 读IOPS")
    print("   - 红色/橙色: 写IOPS")

if __name__ == "__main__":
    main()
