#!/usr/bin/env python3
"""
生成CSV格式的性能对比报告
"""

import csv
import re
from pathlib import Path

def parse_summary_file(file_path):
    """解析FIO汇总文件，提取详细性能指标"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # 提取读写性能数据
        read_match = re.search(r'read: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s.*?slat.*?avg=([0-9.]+).*?clat.*?avg=([0-9.]+)', content, re.DOTALL)
        write_match = re.search(r'write: IOPS=([0-9.k]+), BW=([0-9.]+)MiB/s.*?slat.*?avg=([0-9.]+).*?clat.*?avg=([0-9.]+)', content, re.DOTALL)
        
        def parse_iops(iops_str):
            if 'k' in iops_str:
                return float(iops_str.replace('k', '')) * 1000
            return float(iops_str)
        
        result = {}
        if read_match:
            result['read_iops'] = parse_iops(read_match.group(1))
            result['read_bw'] = float(read_match.group(2))
            result['read_slat'] = float(read_match.group(3))
            result['read_clat'] = float(read_match.group(4))
        
        if write_match:
            result['write_iops'] = parse_iops(write_match.group(1))
            result['write_bw'] = float(write_match.group(2))
            result['write_slat'] = float(write_match.group(3))
            result['write_clat'] = float(write_match.group(4))
            
        return result
    except Exception as e:
        print(f"Error parsing {file_path}: {e}")
        return {}

def generate_csv_report():
    """生成CSV格式的详细对比报告"""
    baseline_path = Path("/check/fio-web-controller/hp")
    test_path = Path("/check/fio_results_20250716_121731")
    
    test_types = ['mixed_rw_4k', 'mixed_rw_128k', 'mixed_rw_1M']
    
    # 准备CSV数据
    csv_data = []
    headers = [
        'Test_Type', 'Metric', 'Baseline_Value', 'Test_Value', 'Ratio', 'Status',
        'Baseline_Latency', 'Test_Latency', 'Latency_Change'
    ]
    
    for test_type in test_types:
        baseline_file = baseline_path / f"{test_type}_summary.txt"
        test_file = test_path / f"{test_type}_summary.txt"
        
        if baseline_file.exists() and test_file.exists():
            baseline_data = parse_summary_file(baseline_file)
            test_data = parse_summary_file(test_file)
            
            # 读性能数据
            if 'read_iops' in baseline_data and 'read_iops' in test_data:
                ratio = test_data['read_iops'] / baseline_data['read_iops']
                status = 'PASS' if ratio >= 1.0 else 'FAIL'
                latency_change = ((test_data['read_clat'] - baseline_data['read_clat']) / baseline_data['read_clat']) * 100
                
                csv_data.append([
                    test_type, 'Read_IOPS', 
                    f"{baseline_data['read_iops']:.0f}", 
                    f"{test_data['read_iops']:.0f}",
                    f"{ratio:.3f}", status,
                    f"{baseline_data['read_clat']:.2f}",
                    f"{test_data['read_clat']:.2f}",
                    f"{latency_change:.1f}%"
                ])
                
                # 读带宽数据
                bw_ratio = test_data['read_bw'] / baseline_data['read_bw']
                csv_data.append([
                    test_type, 'Read_Bandwidth_MiB/s',
                    f"{baseline_data['read_bw']:.1f}",
                    f"{test_data['read_bw']:.1f}",
                    f"{bw_ratio:.3f}", 'PASS' if bw_ratio >= 1.0 else 'FAIL',
                    '', '', ''
                ])
            
            # 写性能数据
            if 'write_iops' in baseline_data and 'write_iops' in test_data:
                ratio = test_data['write_iops'] / baseline_data['write_iops']
                status = 'PASS' if ratio >= 1.0 else 'FAIL'
                latency_change = ((test_data['write_clat'] - baseline_data['write_clat']) / baseline_data['write_clat']) * 100
                
                csv_data.append([
                    test_type, 'Write_IOPS',
                    f"{baseline_data['write_iops']:.0f}",
                    f"{test_data['write_iops']:.0f}",
                    f"{ratio:.3f}", status,
                    f"{baseline_data['write_clat']:.2f}",
                    f"{test_data['write_clat']:.2f}",
                    f"{latency_change:.1f}%"
                ])
                
                # 写带宽数据
                bw_ratio = test_data['write_bw'] / baseline_data['write_bw']
                csv_data.append([
                    test_type, 'Write_Bandwidth_MiB/s',
                    f"{baseline_data['write_bw']:.1f}",
                    f"{test_data['write_bw']:.1f}",
                    f"{bw_ratio:.3f}", 'PASS' if bw_ratio >= 1.0 else 'FAIL',
                    '', '', ''
                ])
    
    # 写入CSV文件
    csv_file = '/check/performance_comparison_detailed.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(csv_data)
    
    print(f"详细CSV报告已生成: {csv_file}")
    
    # 显示CSV内容预览
    print("\nCSV报告预览:")
    print("-" * 100)
    with open(csv_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)

if __name__ == "__main__":
    generate_csv_report()
