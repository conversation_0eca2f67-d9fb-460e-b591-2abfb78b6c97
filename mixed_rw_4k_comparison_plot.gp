
set terminal png size 1600,1000 font "Arial,16"
set output "/check/mixed_rw_4k_comparison_chart.png"

set title "4K 混合读写性能对比\n基准: HP硬盘(测试1) vs 对比: 新硬盘(测试2)" font "Arial,20"
set xlabel "时间 (分钟)" font "Arial,16"
set ylabel "IOPS" font "Arial,16"

set grid
set key outside right top font "Arial,14"

# 设置线条样式 - 更明显的区分
set style line 1 lc rgb "#1f77b4" lw 3 pt 7 ps 1.0                    # 基准读 - 蓝色实线
set style line 2 lc rgb "#ff7f0e" lw 3 pt 7 ps 1.0                    # 基准写 - 橙色实线
set style line 3 lc rgb "#2ca02c" lw 3 pt 9 ps 1.0 dt 2               # 对比读 - 绿色虚线
set style line 4 lc rgb "#d62728" lw 3 pt 9 ps 1.0 dt 2               # 对比写 - 红色虚线

# 设置图例框
set key box

plot "/check/mixed_rw_4k_comparison_trend.dat" using 1:2 with linespoints ls 1 title "基准硬盘(HP) - 读 IOPS", \
     "" using 1:3 with linespoints ls 2 title "基准硬盘(HP) - 写 IOPS", \
     "" using 1:4 with linespoints ls 3 title "新硬盘 - 读 IOPS", \
     "" using 1:5 with linespoints ls 4 title "新硬盘 - 写 IOPS"

# 添加清晰的文本说明
set label "块大小: 4K" at graph 0.02, graph 0.95 font "Arial,14" tc rgb "black" boxed
set label "实线: 基准硬盘(HP)" at graph 0.02, graph 0.90 font "Arial,12" tc rgb "blue"
set label "虚线: 新硬盘" at graph 0.02, graph 0.87 font "Arial,12" tc rgb "green"
set label "蓝色/绿色: 读IOPS" at graph 0.02, graph 0.84 font "Arial,12" tc rgb "black"
set label "橙色/红色: 写IOPS" at graph 0.02, graph 0.81 font "Arial,12" tc rgb "black"
