
set terminal png size 1400,900 font "Arial,14"
set output "/check/mixed_rw_4k_comparison_chart.png"

set title "4K 混合读写性能对比 - 时间序列对比" font "Arial,18"
set xlabel "时间 (分钟)" font "Arial,14"
set ylabel "IOPS" font "Arial,14"

set grid
set key outside right top

# 设置线条样式
set style line 1 lc rgb "blue" lw 2 pt 7 ps 0.8
set style line 2 lc rgb "red" lw 2 pt 7 ps 0.8
set style line 3 lc rgb "green" lw 2 pt 9 ps 0.8 dt 2
set style line 4 lc rgb "orange" lw 2 pt 9 ps 0.8 dt 2

# 设置图例
set key box opaque

plot "/check/mixed_rw_4k_comparison_trend.dat" using 1:2 with linespoints ls 1 title "测试1(基准) - 读 IOPS", \
     "" using 1:3 with linespoints ls 2 title "测试1(基准) - 写 IOPS", \
     "" using 1:4 with linespoints ls 3 title "测试2 - 读 IOPS", \
     "" using 1:5 with linespoints ls 4 title "测试2 - 写 IOPS"

# 添加文本说明
set label "块大小: 4K" at graph 0.02, graph 0.95 font "Arial,12"
set label "实线: 测试1(基准)" at graph 0.02, graph 0.90 font "Arial,10"
set label "虚线: 测试2" at graph 0.02, graph 0.87 font "Arial,10"
